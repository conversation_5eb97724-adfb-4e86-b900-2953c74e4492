<?php $__env->startSection('title', 'Login - ARDFYA'); ?>

<?php $__env->startSection('content'); ?>
<div class="py-10 md:py-16 flex-grow">
    <div class="container mx-auto px-4">
        <div class="flex justify-center">
            <div class="w-full max-w-md">
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200">
                    <div class="bg-green-700 text-white px-6 py-3 text-center">
                        <h1 class="text-xl font-semibold">Login</h1>
                    </div>

                    <div class="px-6 py-6">
                        <form method="POST" action="<?php echo e(route('login')); ?>" class="space-y-4">
                            <?php echo csrf_field(); ?>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                <input id="email" type="email" 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-1 focus:ring-green-700 focus:border-green-700 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email" autofocus placeholder="<EMAIL>">

                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600">
                                        <?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                                <input id="password" type="password" 
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-1 focus:ring-green-700 focus:border-green-700 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    name="password" required autocomplete="current-password" placeholder="********">

                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600">
                                        <?php echo e($message); ?>

                                    </p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="flex items-center">
                                <input class="h-4 w-4 text-green-700 border-gray-300 rounded focus:ring-green-700" 
                                    type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>

                                <label class="ml-2 block text-sm text-gray-700" for="remember">
                                    Remember Me
                                </label>
                            </div>

                            <div>
                                <button type="submit" class="w-full bg-green-700 text-white px-4 py-2 rounded font-semibold hover:bg-green-800 transition-colors">
                                    Login
                                </button>
                            </div>
                            
                            <div class="flex justify-end">
                                <?php if(Route::has('password.request')): ?>
                                    <a class="text-sm text-green-700 hover:underline" href="<?php echo e(route('password.request')); ?>">
                                        Forgot Your Password?
                                    </a>
                                <?php endif; ?>
                            </div>
                            
                            <?php if(Route::has('register')): ?>
                                <div class="text-center mt-4 pt-4 border-t border-gray-200">
                                    <p class="text-sm text-gray-600">
                                        Don't have an account? <a href="<?php echo e(route('register')); ?>" class="text-green-700 font-medium hover:underline">Register here</a>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Removed the duplicate footer - using only the main footer from the layout -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Codingan\CURSOR TA\ardfya_v2\resources\views/auth/login.blade.php ENDPATH**/ ?>