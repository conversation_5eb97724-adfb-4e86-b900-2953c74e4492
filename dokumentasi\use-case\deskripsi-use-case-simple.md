# Deskripsi Use Case - ARDFYA

## Aktor

### Guest
Pengunjung website yang belum login dan dapat mengakses fitur publik.

### Customer
Pen<PERSON><PERSON> yang sudah terdaftar dan login, dapat mengakses fitur khusus customer.

### Admin
Administrator sistem dengan akses penuh ke fitur manajemen.

## Public Features

### UC1: Browse Homepage
**Aktor:** Guest  
**Deskripsi:** Mengakses dan melihat halaman utama website.  
**Alur Utama:**
1. Guest mengakses URL website
2. Sistem menampilkan halaman beranda dengan informasi perusahaan, layanan, dan portfolio

### UC2: View Portfolio
**Aktor:** Guest  
**Deskripsi:** Melihat portfolio proyek yang telah diselesaikan.  
**Alur Utama:**
1. Guest mengakses halaman portfolio
2. Sistem menampilkan daftar portfolio dengan gambar dan deskripsi

### UC3: Submit Inquiry
**Aktor:** Guest, Customer  
**Deskripsi:** Mengajukan permintaan layanan.  
**Alur Utama:**
1. Aktor mengisi form inquiry (layanan, informasi kontak, detail proyek)
2. Aktor mengirimkan form
3. Sistem menyimpan inquiry dan mengirimkan notifikasi ke admin

### UC4: Register Account
**Aktor:** Guest  
**Deskripsi:** Mendaftar akun baru sebagai customer.  
**Alur Utama:**
1. Guest mengisi form registrasi (nama, email, password)
2. Sistem membuat akun baru
3. Sistem melakukan login otomatis (include UC5)

### UC5: Login
**Aktor:** Guest  
**Deskripsi:** Masuk ke sistem dengan akun yang sudah terdaftar.  
**Alur Utama:**
1. Guest memasukkan email dan password
2. Sistem memvalidasi kredensial
3. Sistem membuat session dan mengarahkan ke dashboard

## Customer Features

### UC6: View Dashboard
**Aktor:** Customer  
**Deskripsi:** Melihat dashboard dengan ringkasan proyek dan inquiry.  
**Alur Utama:**
1. Customer login ke sistem
2. Sistem menampilkan dashboard dengan ringkasan proyek dan inquiry

### UC7: Track Projects
**Aktor:** Customer  
**Deskripsi:** Melacak progress proyek yang sedang berjalan.  
**Alur Utama:**
1. Customer mengakses halaman proyek
2. Sistem menampilkan daftar proyek dengan status dan progress

### UC8: Chat with Admin
**Aktor:** Customer  
**Deskripsi:** Berkomunikasi dengan admin melalui fitur chat.  
**Alur Utama:**
1. Customer mengakses halaman chat
2. Customer mengirim pesan
3. Sistem menyimpan dan mengirimkan pesan ke admin (include UC17)

### UC9: View Contracts
**Aktor:** Customer  
**Deskripsi:** Melihat kontrak proyek.  
**Alur Utama:**
1. Customer mengakses halaman kontrak
2. Sistem menampilkan daftar kontrak
3. Customer dapat mengunduh kontrak (include UC10)

### UC10: Download Contract
**Aktor:** Customer  
**Deskripsi:** Mengunduh dokumen kontrak.  
**Alur Utama:**
1. Customer memilih opsi download pada kontrak
2. Sistem menghasilkan file PDF kontrak
3. Customer menerima file PDF kontrak

### UC11: Manage Profile
**Aktor:** Customer  
**Deskripsi:** Mengelola profil customer.  
**Alur Utama:**
1. Customer mengakses halaman profil
2. Customer dapat melihat dan mengedit informasi profil

## Admin Features

### UC12: Manage Customers
**Aktor:** Admin  
**Deskripsi:** Mengelola data customer.  
**Alur Utama:**
1. Admin mengakses menu manajemen customer
2. Admin dapat melihat, mengedit, dan menghapus data customer

### UC13: Manage Inquiries
**Aktor:** Admin  
**Deskripsi:** Mengelola inquiry yang masuk.  
**Alur Utama:**
1. Admin mengakses menu manajemen inquiry
2. Admin dapat melihat, memproses, dan mengkonversi inquiry ke proyek (extend UC14)

### UC14: Manage Projects
**Aktor:** Admin  
**Deskripsi:** Mengelola proyek yang sedang berjalan.  
**Alur Utama:**
1. Admin mengakses menu manajemen proyek
2. Admin dapat membuat, mengedit, dan mengupdate status proyek
3. Admin dapat membuat kontrak untuk proyek (extend UC16)

### UC15: Manage Portfolio
**Aktor:** Admin  
**Deskripsi:** Mengelola portfolio proyek.  
**Alur Utama:**
1. Admin mengakses menu portfolio
2. Admin dapat membuat, mengedit, dan menghapus portfolio
3. Admin dapat mengupload gambar portfolio

### UC16: Create Contracts
**Aktor:** Admin  
**Deskripsi:** Membuat kontrak untuk proyek.  
**Alur Utama:**
1. Admin memilih proyek
2. Admin mengisi detail kontrak (nilai, syarat, jadwal)
3. Sistem menghasilkan dokumen kontrak

### UC17: Chat with Customer
**Aktor:** Admin  
**Deskripsi:** Berkomunikasi dengan customer melalui fitur chat.  
**Alur Utama:**
1. Admin mengakses menu chat
2. Admin memilih customer
3. Admin mengirim dan menerima pesan

## Relationships

### Include Relationships
- **UC4 → UC5**: Register Account include Login
- **UC9 → UC10**: View Contracts include Download Contract
- **UC8 → UC17**: Chat with Admin include Chat with Customer

### Extend Relationships
- **UC13 → UC14**: Manage Inquiries extend to Manage Projects
- **UC14 → UC16**: Manage Projects extend to Create Contracts

## Alur Bisnis Utama

### 1. Proses Inquiry ke Proyek
```
Guest/Customer → Submit Inquiry → Admin Review → Convert to Project → Create Contract → Customer Track Project
```

### 2. Proses Registrasi dan Login
```
Guest → Register Account → Login → View Dashboard → Access Features
```

### 3. Proses Komunikasi
```
Customer → Chat with Admin ↔ Admin → Chat with Customer
```
