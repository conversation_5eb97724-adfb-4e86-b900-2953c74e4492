# Activity Diagram - Admin Kelola Portfolio

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses menu kelola portfolio]
    
    subgraph Admin ["👤 Admin"]
        A1[Akses menu kelola portfolio]
        A2[Lihat daftar portfolio]
        A3[<PERSON><PERSON>h tombol aksi]
        A4[Lakukan input/konfirmasi]
        A5[<PERSON><PERSON> hasil perubahan]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load halaman portfolio]
        S2[Tampilkan daftar portfolio]
        S3[Proses request aksi]
        S4[Tampilkan halaman/form sesuai aksi]
        S5[Simpan perubahan ke database]
        S6[Update tampilan daftar portfolio]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> A2
    A2 --> A3
    A3 --> S3
    S3 --> S4
    S4 --> A4
    A4 --> S5
    S5 --> S6
    S6 --> A5
    A5 --> End([Stop])
    
    %% Styling
    classDef adminClass fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5 adminClass
    class S1,S2,S3,S4,S5,S6 sistemClass
    class Start,End startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja admin saat mengelola portfolio proyek:

### Swimlane Admin:
- **Akses menu kelola portfolio**: Admin mengklik menu portfolio di dashboard
- **Lihat daftar portfolio**: Admin melihat semua portfolio yang tersedia
- **Pilih tombol aksi**: Admin memilih aksi (tambah, edit, hapus, lihat detail)
- **Lakukan input/konfirmasi**: Admin mengisi form atau konfirmasi aksi
- **Lihat hasil perubahan**: Admin melihat hasil dari aksi yang dilakukan

### Swimlane Sistem:
- **Load halaman portfolio**: Sistem memuat halaman manajemen portfolio
- **Tampilkan daftar portfolio**: Sistem menampilkan daftar portfolio dengan opsi aksi
- **Proses request aksi**: Sistem memproses permintaan aksi dari admin
- **Tampilkan halaman/form sesuai aksi**: Sistem menampilkan form atau halaman yang sesuai
- **Simpan perubahan ke database**: Sistem menyimpan perubahan ke database
- **Update tampilan daftar portfolio**: Sistem memperbarui tampilan daftar

### Jenis Aksi Portfolio:
1. **Tambah Portfolio Baru**:
   - Form input: judul, deskripsi, kategori, gambar
   - Upload gambar portfolio
   - Simpan data portfolio baru

2. **Edit Portfolio Existing**:
   - Load data portfolio yang dipilih
   - Form edit dengan data existing
   - Update data portfolio

3. **Hapus Portfolio**:
   - Konfirmasi penghapusan
   - Hapus data dan file terkait

4. **Lihat Detail Portfolio**:
   - Tampilkan informasi lengkap
   - Preview gambar dan deskripsi

### Karakteristik:
- **Actor**: Admin
- **Trigger**: Admin ingin mengelola portfolio proyek
- **Precondition**: Admin sudah login dan memiliki akses ke menu portfolio
- **Postcondition**: Portfolio berhasil dikelola dan perubahan tersimpan

### Alur Proses:
1. Admin mengakses menu kelola portfolio
2. Sistem menampilkan daftar portfolio dengan tombol aksi
3. Admin memilih aksi yang diinginkan (CRUD operations)
4. Sistem menampilkan form atau halaman sesuai aksi
5. Admin melakukan input atau konfirmasi
6. Sistem menyimpan perubahan dan update tampilan
