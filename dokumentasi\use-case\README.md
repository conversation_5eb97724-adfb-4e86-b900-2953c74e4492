# Use Case Documentation - Sistem ARDFYA v2.1

## 📋 Gambaran Umum

Folder ini berisi dokumentasi lengkap Use Case untuk Sistem Manajemen Konstruksi ARDFYA v2.1. Dokumentasi ini dibuat dalam bahasa Indonesia untuk memudahkan pemahaman dan konsistensi penggunaan bahasa.

## 📁 Struktur File

```
use-case/
├── README.md                           # File ini - panduan penggunaan
├── use-case-professional.puml         # ⭐ REKOMENDASI: Diagram profesional
├── use-case-clean.puml                # Diagram bersih dan sederhana
├── use-case-final.puml                # Diagram minimal
├── use-case-overview.puml             # Diagram overview sistem
├── use-case-simplified.puml           # Diagram use case inti
├── use-case-diagram-indonesia.puml    # Diagram use case lengkap
└── deskripsi-use-case.md              # Deskripsi detail setiap Use Case
```

## ⭐ Rekomendasi Penggunaan

### 🏆 **use-case-balanced.puml** (TERBAIK - SEDERHANA)
- **Desain**: Profesional, sederhana, mudah dipahami
- **Layout**: Horizontal dengan flow lurus
- **Isi**: 3 aktor, 5 use case inti
- **Cocok untuk**: Presentasi stakeholder, overview sistem

### 🎯 **use-case-professional.puml** (LENGKAP)
- **Desain**: Profesional dengan detail lengkap
- **Layout**: Horizontal, komprehensif
- **Isi**: 3 aktor, 18 use case detail
- **Cocok untuk**: Dokumentasi teknis, development reference

### 📋 **use-case-clean.puml** (MINIMAL)
- **Desain**: Bersih dan minimal
- **Layout**: Compact
- **Isi**: 3 aktor, 5 use case dasar
- **Cocok untuk**: Draft awal, quick reference

## 🎯 Tujuan Dokumentasi

1. **Reverse Engineering**: Mendokumentasikan fungsionalitas existing system
2. **Analisis Sistem**: Memahami interaksi antara aktor dan sistem
3. **Pengembangan Lanjutan**: Referensi untuk pengembangan fitur baru
4. **Komunikasi Tim**: Media komunikasi antara developer, analyst, dan stakeholder

## 👥 Aktor Sistem

### 1. Pengunjung (Guest)
- **Deskripsi**: Pengguna yang belum terdaftar atau belum login
- **Akses**: Fitur publik (beranda, portfolio, kontak, registrasi)
- **Batasan**: Tidak dapat mengakses fitur yang memerlukan autentikasi

### 2. Pelanggan (Customer)
- **Deskripsi**: Pengguna terdaftar yang dapat mengajukan proyek
- **Akses**: Dashboard pelanggan, inquiry, chat, tracking proyek
- **Batasan**: Hanya dapat melihat data proyek mereka sendiri

### 3. Administrator (Admin)
- **Deskripsi**: Pengelola sistem dengan akses penuh
- **Akses**: Semua fitur sistem termasuk manajemen data
- **Batasan**: Tidak ada (full access)

## 📊 Kategori Use Case

### 1. Fitur Publik (9 Use Cases)
- UC01-UC09: Fitur yang dapat diakses tanpa login
- Fokus: Informasi perusahaan, portfolio, registrasi

### 2. Area Pelanggan (11 Use Cases)
- UC10-UC20: Fitur khusus pelanggan terdaftar
- Fokus: Manajemen proyek, komunikasi, tracking

### 3. Manajemen Admin (10 Use Cases)
- UC21-UC30: Fitur administrasi sistem
- Fokus: Pengelolaan data, analitik, konfigurasi

### 4. Manajemen Portfolio (7 Use Cases)
- UC31-UC37: Fitur baru v2.1 untuk portfolio
- Fokus: CRUD portfolio, kategorisasi, analitik

### 5. Manajemen Inquiry (5 Use Cases)
- UC38-UC42: Pengelolaan inquiry pelanggan
- Fokus: Workflow inquiry ke proyek

### 6. Manajemen Proyek (5 Use Cases)
- UC43-UC47: Pengelolaan proyek konstruksi
- Fokus: Lifecycle proyek, dokumentasi

### 7. Sistem Komunikasi (5 Use Cases)
- UC48-UC52: Fitur chat dan komunikasi
- Fokus: Real-time messaging, notifikasi

## 🔄 Workflow Utama

### 1. Customer Journey
```
Pengunjung → Registrasi → Login → Dashboard → Inquiry → Proyek → Chat → Kontrak
```

### 2. Admin Workflow
```
Login Admin → Dashboard → Kelola Inquiry → Buat Proyek → Buat Kontrak → Chat Support
```

### 3. Portfolio Management
```
Admin → Buat Portfolio → Upload Gambar → Set Kategori → Publikasi → Analitik
```

## 🛠️ Cara Menggunakan Dokumentasi

### 1. Melihat Diagram Use Case
- Buka file `use-case-diagram-indonesia.puml`
- Gunakan PlantUML viewer/editor untuk melihat diagram
- Online viewer: http://www.plantuml.com/plantuml/uml/

### 2. Memahami Detail Use Case
- Baca file `deskripsi-use-case.md`
- Setiap use case memiliki:
  - Deskripsi singkat
  - Aktor yang terlibat
  - Prasyarat
  - Alur utama (main flow)

### 3. Implementasi Development
- Gunakan use case sebagai referensi requirement
- Mapping use case ke controller dan route Laravel
- Validasi implementasi dengan skenario use case

## 🔗 Relasi Use Case

### Include Relationships
- **UC06 → UC07**: Registrasi otomatis login
- **UC15 → UC16**: Download kontrak include preview

### Extend Relationships
- **UC12 → UC40**: Inquiry dapat dikonversi ke proyek
- **UC24 → UC47**: Proyek dapat dibuatkan kontrak

## 🏗️ Mapping ke Arsitektur Laravel

### Controllers
```php
// Guest Features
HomeController::class           // UC01, UC02, UC03, UC08, UC09
ContactController::class        // UC04
InquiryController::class        // UC05
Auth\RegisterController::class  // UC06
Auth\LoginController::class     // UC07

// Customer Features
Customer\DashboardController::class  // UC10
Customer\ProfileController::class    // UC11
InquiryController::class            // UC12
Customer\ProjectController::class    // UC13, UC14
Customer\ContractController::class   // UC15, UC16
MessageController::class            // UC17, UC18, UC19

// Admin Features
Admin\DashboardController::class     // UC21
Admin\CustomerController::class      // UC22
Admin\InquiryController::class       // UC23, UC38-UC42
Admin\ProjectController::class       // UC24, UC43-UC47
Admin\ContractController::class      // UC25
Admin\ServiceController::class       // UC26
Admin\AnalyticsController::class     // UC27
Admin\ReportsController::class       // UC28
Admin\MessageController::class       // UC29
Admin\ConfigController::class        // UC30
Admin\PortfolioController::class     // UC31-UC37

// Communication
ChatController::class               // UC48-UC52
```

### Models
```php
User::class         // Customer & Admin actors
Service::class      // UC09, UC26
Portfolio::class    // UC02, UC03, UC31-UC37
Inquiry::class      // UC05, UC12, UC23, UC38-UC42
Project::class      // UC13, UC14, UC24, UC43-UC47
Contract::class     // UC15, UC16, UC25, UC47
Message::class      // UC17-UC19, UC29, UC48-UC52
```

## 📈 Fitur Baru v2.1

### Portfolio Management
- **UC31-UC37**: Sistem portfolio terintegrasi
- **Fitur**: CRUD portfolio, kategorisasi, featured portfolio
- **Integrasi**: Tampil di homepage dan halaman portfolio

### Enhanced Communication
- **UC48-UC52**: Real-time chat dengan notifikasi
- **Teknologi**: Laravel Echo + Pusher
- **Fitur**: Online status, read receipt, search history

### Improved Admin Analytics
- **UC27, UC37**: Dashboard analitik yang lebih komprehensif
- **Metrics**: Portfolio views, inquiry conversion, project status

## 🔧 Tools dan Teknologi

### PlantUML
- **Purpose**: Membuat diagram use case
- **Format**: `.puml` files
- **Viewer**: VS Code PlantUML extension, online viewer

### Laravel Framework
- **Version**: 12.x
- **Features**: Eloquent ORM, Blade templating, Middleware
- **Real-time**: Laravel Echo, Pusher

### Database
- **Development**: SQLite
- **Production**: MySQL/PostgreSQL
- **ORM**: Eloquent relationships

## 📝 Maintenance

### Update Diagram
1. Edit file `.puml` dengan PlantUML syntax
2. Regenerate diagram image
3. Update dokumentasi jika ada perubahan use case

### Update Deskripsi
1. Edit file `deskripsi-use-case.md`
2. Tambah use case baru dengan format yang sama
3. Update relasi dan mapping jika diperlukan

### Versioning
- **v2.1**: Current version dengan portfolio management
- **Future**: Planned features berdasarkan use case analysis

## 🎯 Next Steps

1. **Implementasi Testing**: Buat test case berdasarkan use case
2. **API Documentation**: Mapping use case ke API endpoints
3. **User Manual**: Buat panduan user berdasarkan use case
4. **Performance Testing**: Test skenario use case untuk performance

---

**Catatan**: Dokumentasi ini dibuat sebagai bagian dari proses reverse engineering dan akan terus diperbarui seiring dengan pengembangan sistem.
