@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Admin Kelola Proyek**

actor Admin
participant "Admin Panel" as Panel
participant "Database" as DB
participant "Notification" as Notif

Admin -> Panel: Akses menu kelola proyek
activate Panel

Panel -> DB: Load daftar proyek
activate DB
DB --> Panel: Return daftar proyek dengan status
deactivate DB

Panel --> Admin: Tampilkan halaman proyek
Panel --> Admin: <PERSON><PERSON>lkan daftar proyek

Admin -> Panel: <PERSON>lih proyek yang akan dikelola
Panel -> DB: Load detail proyek
activate DB
DB --> Panel: Return detail proyek dan timeline
deactivate DB

Panel --> Admin: Tampilkan detail proyek

Admin -> Panel: Pilih aksi kelola proyek


Admin -> Panel: Input perubahan data
Admin -> Panel: Upload file hasil (opsional)
Admin -> Panel: Konfirmasi perubahan

Panel -> DB: Validasi dan simpan perubahan
activate DB
DB --> Panel: Return konfirmasi update
deactivate DB

Panel -> Notif: Kirim notifikasi ke customer
activate Notif
Notif --> Panel: Confirm notifikasi terkirim
deactivate Notif

Panel --> Admin: Tampilkan konfirmasi berhasil
Panel --> Admin: Update tampilan daftar proyek

Admin -> Admin: Lihat hasil perubahan

deactivate Panel

@enduml
