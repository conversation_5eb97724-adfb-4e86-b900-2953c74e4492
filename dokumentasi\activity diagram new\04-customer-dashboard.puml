@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho

title **Activity Diagram - Customer Dashboard**

|Customer|
start
:Login ke sistem;

|Sistem|
:Validasi session customer;
:Load dashboard customer;
:Tampilkan menu navigasi;
:Tam<PERSON><PERSON>an ringkasan proyek;
:Tampilkan status inquiry;

|Customer|
:Lihat dashboard;
:Pilih menu yang diinginkan;

|Sistem|
if (<PERSON>u yang dipilih?) then (Lacak Proyek)
  :Redirect ke halaman proyek;
  stop
else if (Chat)
  :Redirect ke halaman chat;
  stop
else if (Ajukan Permintaan)
  :Redirect ke halaman inquiry;
  stop
else (Logout)
  :Hapus session customer;
  :Redirect ke halaman login;
  stop
endif

@enduml
