@startuml Use Case Diagram - ARDFYA

!theme plain
skinparam handwritten false
skinparam shadowing false

' Styling sederhana
skinparam usecase {
    BackgroundColor #E1F5FE
    BorderColor #0288D1
    FontSize 12
}

skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #01579B
    FontSize 12
}

skinparam package {
    BackgroundColor #F5F5F5
    BorderColor #757575
    FontSize 12
}

skinparam rectangle {
    BackgroundColor #FAFAFA
    BorderColor #424242
    FontSize 14
}

title Use Case Diagram - ARDFYA Construction Management System

' Actors
actor "Guest" as G
actor "Customer" as C
actor "Admin" as A

' System boundary
rectangle "ARDFYA System" {
  
  ' Guest Use Cases
  package "Public Features" {
    usecase "Browse Homepage" as UC1
    usecase "View Portfolio" as UC2
    usecase "Submit Inquiry" as UC3
    usecase "Register Account" as UC4
    usecase "Login" as UC5
  }
  
  ' Customer Use Cases
  package "Customer Features" {
    usecase "View Dashboard" as UC6
    usecase "Track Projects" as UC7
    usecase "Chat with Admin" as UC8
    usecase "View Contracts" as UC9
    usecase "Download Contract" as UC10
    usecase "Manage Profile" as UC11
  }
  
  ' Admin Use Cases
  package "Admin Features" {
    usecase "Manage Customers" as UC12
    usecase "Manage Inquiries" as UC13
    usecase "Manage Projects" as UC14
    usecase "Manage Portfolio" as UC15
    usecase "Create Contracts" as UC16
    usecase "Chat with Customer" as UC17
  }
}

' Guest relationships
G --> UC1
G --> UC2
G --> UC3
G --> UC4
G --> UC5

' Customer relationships
C --> UC6
C --> UC7
C --> UC8
C --> UC9
C --> UC11
C --> UC3

' Admin relationships
A --> UC12
A --> UC13
A --> UC14
A --> UC15
A --> UC16
A --> UC17

' Include relationships
UC4 ..> UC5 : <<include>>
UC9 ..> UC10 : <<include>>
UC13 ..> UC14 : <<extend>>
UC14 ..> UC16 : <<extend>>
UC8 ..> UC17 : <<include>>

@enduml
