@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Admin Kelola Portfolio**

actor Admin
participant "Admin Panel" as Panel
participant "Database" as DB

Admin -> Panel: Akses menu kelola portfolio
activate Panel

Panel -> DB: Load daftar portfolio
activate DB
DB --> Panel: Return daftar portfolio
deactivate DB

Panel --> Admin: Tampilkan halaman portfolio
Panel --> Admin: Tampilkan daftar portfolio

Admin -> Panel: <PERSON><PERSON><PERSON> aksi (tambah/edit/hapus)


Panel --> Admin: Tampilkan form sesuai aksi

Admin -> Panel: Input/update data portfolio
Admin -> Panel: Upload gambar (jika ada)
Admin -> Panel: Konfirmasi aksi

Panel -> DB: Validasi data portfolio
activate DB
DB --> Panel: Return hasil validasi
deactivate DB

Panel -> DB: Simpan perubahan portfolio
activate DB
DB --> Panel: Return konfirmasi berhasil
deactivate DB

Panel --> Admin: Tampilkan konfirmasi berhasil
Panel --> Admin: Update tampilan daftar portfolio

Admin -> Admin: Lihat hasil perubahan

deactivate Panel

@enduml
