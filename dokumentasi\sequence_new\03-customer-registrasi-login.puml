@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Customer Login**

actor Customer
participant "Website ARDFYA" as Website
participant "Auth System" as Auth
participant "Database" as DB

Customer -> Website: Klik tombol login
activate Website

Website --> Customer: Tampilkan form login

Customer -> Website: Input email dan password
Website -> Auth: Validasi kredensial
activate Auth

Auth -> DB: Cek data customer
activate DB
DB --> Auth: Return data customer
deactivate DB

Auth --> Website: Return hasil validasi



Website --> Customer: Redirect ke dashboard customer

deactivate Auth
deactivate Website

@enduml
