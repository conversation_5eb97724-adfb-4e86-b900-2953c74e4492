@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho
skinparam nodesep 60
skinparam ranksep 80
skinparam minlen 4
skinparam padding 15

title **Activity Diagram - Guest Lihat Portfolio**

|Guest|
start
:A<PERSON><PERSON> halaman portfolio;
:Pilih kategori portfolio;

|Sistem|
:Load data portfolio;
:Filter berdasarkan kategori;
:Tampilkan daftar portfolio;

|Guest|
:Lihat daftar portfolio;
:Klik detail portfolio;

|Sistem|
:Tampilkan detail portfolio;
:<PERSON><PERSON><PERSON><PERSON> gambar dan deskripsi;

|Guest|
:<PERSON>hat detail portfolio;

|Sistem|
stop

@enduml
