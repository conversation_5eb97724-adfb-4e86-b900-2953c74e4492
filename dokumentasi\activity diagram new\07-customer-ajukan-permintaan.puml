@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho
skinparam nodesep 60
skinparam ranksep 80
skinparam minlen 4
skinparam padding 15

title **Activity Diagram - Customer Ajukan Permin<PERSON>an**

|Customer|
start
:Akses menu ajukan permintaan;

|Sistem|
:Load form inquiry;
:Tampilkan form permintaan;

|Customer|
:Isi form permintaan;
:Input judul permintaan;
:Input deskripsi kebutuhan;
:Upload file pendukung (opsional);
:Submit form;

|Sistem|
:Validasi data form;
if (Data valid?) then (Ya)
  :Simpan inquiry ke database;
  :Generate nomor tiket;
  :<PERSON><PERSON> notifikasi ke admin;
  :Kirim email konfirmasi ke customer;

  |Customer|
  :Terima konfirmasi pengajuan;
  :Catat nomor tiket;

  |Sistem|
  stop
else (Tidak)
  :Tampilkan pesan error;

  |Customer|
  :Perbaiki form;

  |Sistem|
  stop
endif

@enduml
