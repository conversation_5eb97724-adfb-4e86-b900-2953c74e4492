# Activity Diagram - Admin Buat Kontrak

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses menu buat kontrak]
    
    subgraph Admin ["👤 Admin"]
        A1[Akses menu buat kontrak]
        A2[Pilih proyek untuk kontrak]
        A3[Isi detail kontrak]
        A4[Input nilai kontrak]
        A5[Input syarat dan ketentuan]
        A6[Input jadwal pembayaran]
        A7[Preview kontrak]
        A8[Review kontrak]
        A9{Kontrak sudah benar?}
        A10[Submit kontrak]
        A11[Kontrak berhasil dibuat]
        A12[Edit kontrak]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load halaman kontrak]
        S2[Tampilkan daftar proyek]
        S3[Load data proyek]
        S4[Tampilkan form kontrak]
        S5[Generate preview kontrak PDF]
        S6[Simpan kontrak ke database]
        S7[Generate kontrak PDF final]
        S8[Kirim notifikasi ke customer]
        S9[Update status proyek]
        S10[Kembali ke form edit]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> A2
    A2 --> S3
    S3 --> S4
    S4 --> A3
    A3 --> A4
    A4 --> A5
    A5 --> A6
    A6 --> A7
    A7 --> S5
    S5 --> A8
    A8 --> A9
    
    %% Decision branches
    A9 -->|Ya| A10
    A10 --> S6
    S6 --> S7
    S7 --> S8
    S8 --> S9
    S9 --> A11
    A11 --> End1([Stop])
    
    A9 -->|Tidak| A12
    A12 --> S10
    S10 --> End2([Stop])
    
    %% Styling
    classDef adminClass fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5,A6,A7,A8,A10,A11,A12 adminClass
    class S1,S2,S3,S4,S5,S6,S7,S8,S9,S10 sistemClass
    class A9 decisionClass
    class Start,End1,End2 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja admin saat membuat kontrak untuk proyek customer:

### Swimlane Admin:
- **Akses menu buat kontrak**: Admin mengklik menu buat kontrak
- **Pilih proyek untuk kontrak**: Admin memilih proyek yang akan dibuatkan kontrak
- **Isi detail kontrak**: Admin mengisi informasi detail kontrak
- **Input nilai kontrak**: Admin memasukkan nilai/harga kontrak
- **Input syarat dan ketentuan**: Admin menambahkan syarat dan ketentuan
- **Input jadwal pembayaran**: Admin mengatur jadwal pembayaran
- **Preview kontrak**: Admin melihat preview kontrak yang akan dibuat
- **Review kontrak**: Admin meninjau kembali kontrak
- **Submit kontrak**: Admin mengirim kontrak final (jika sudah benar)
- **Edit kontrak**: Admin mengedit kontrak (jika ada yang perlu diperbaiki)

### Swimlane Sistem:
- **Load halaman kontrak**: Sistem memuat halaman pembuatan kontrak
- **Tampilkan daftar proyek**: Sistem menampilkan proyek yang bisa dibuatkan kontrak
- **Load data proyek**: Sistem memuat data proyek yang dipilih
- **Tampilkan form kontrak**: Sistem menampilkan form pembuatan kontrak
- **Generate preview kontrak PDF**: Sistem membuat preview kontrak dalam format PDF
- **Simpan kontrak ke database**: Sistem menyimpan data kontrak
- **Generate kontrak PDF final**: Sistem membuat file PDF kontrak final
- **Kirim notifikasi ke customer**: Sistem mengirim notifikasi ke customer
- **Update status proyek**: Sistem mengupdate status proyek
- **Kembali ke form edit**: Sistem kembali ke form edit untuk perbaikan

### Decision Points:
- **Kontrak sudah benar?**
  - **Ya**: Submit kontrak → Simpan → Generate PDF → Kirim notifikasi
  - **Tidak**: Edit kontrak → Kembali ke form edit

### Komponen Kontrak:
1. **Detail Kontrak**:
   - Informasi proyek
   - Informasi customer
   - Deskripsi pekerjaan

2. **Nilai Kontrak**:
   - Total biaya proyek
   - Rincian biaya
   - Mata uang

3. **Syarat dan Ketentuan**:
   - Ketentuan pembayaran
   - Ketentuan penyelesaian
   - Ketentuan pembatalan

4. **Jadwal Pembayaran**:
   - Pembayaran DP
   - Pembayaran progress
   - Pembayaran final

### Karakteristik:
- **Actor**: Admin
- **Trigger**: Admin ingin membuat kontrak untuk proyek
- **Precondition**: Ada proyek yang perlu dibuatkan kontrak
- **Postcondition**: Kontrak tersimpan, PDF terbuat, dan customer mendapat notifikasi

### Alur Proses:
1. Admin mengakses menu buat kontrak
2. Sistem menampilkan daftar proyek yang bisa dibuatkan kontrak
3. Admin memilih proyek dan mengisi form kontrak lengkap
4. Sistem generate preview PDF untuk review admin
5. Jika sudah benar: sistem simpan, buat PDF final, dan kirim notifikasi
6. Jika belum benar: admin dapat mengedit kembali kontrak
