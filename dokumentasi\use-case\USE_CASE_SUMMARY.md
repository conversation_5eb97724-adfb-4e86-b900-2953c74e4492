# Use Case Diagram - ARDFYA v2.1 Summary

## 📋 Overview

Use Case Diagram lengkap untuk sistem ARDFYA v2.1 yang menggambarkan semua fitur dan interaksi antara aktor (Guest, Customer, Admin) dengan sistem manajemen konstruksi dan arsitektur.

## 👥 Actors

### 1. **Guest** 
- Pengunjung website yang belum login
- Dapat mengakses fitur publik
- Dapat mendaftar dan login

### 2. **Customer**
- Pelanggan yang sudah terdaftar dan login
- Dapat mengelola proyek dan berkomunikasi dengan admin
- Memiliki akses ke dashboard pribadi

### 3. **Admin**
- Administrator sistem dengan akses penuh
- Mengelola semua aspek sistem
- Dapat berkomunikasi dengan customer

## 📊 Use Case Statistics

| Package | Use Cases | Description |
|---------|-----------|-------------|
| **Public Features** | 8 UC | Fitur untuk Guest |
| **Customer Features** | 11 UC | Fitur untuk Customer |
| **Admin Management** | 9 UC | Fitur manajemen Admin |
| **Portfolio Management** | 5 UC | Manajemen portfolio |
| **Inquiry Management** | 4 UC | Manajemen inquiry |
| **Project Management** | 5 UC | Manajemen proyek |
| **Communication System** | 5 UC | Sistem komunikasi |
| **Total** | **47 UC** | **Total Use Cases** |

## 🔗 Include Relationships

Include relationships menunjukkan use case yang **selalu** dijalankan sebagai bagian dari use case lain:

| Base Use Case | Included Use Case | Description |
|---------------|-------------------|-------------|
| UC6: Register Account | UC7: Login to System | Registrasi otomatis login |
| UC15: View My Contracts | UC16: Download Contract PDF | Kontrak include download |
| UC23: Manage Inquiries | UC38: Review Inquiry | Kelola inquiry include review |
| UC24: Manage Projects | UC44: Update Project Status | Kelola proyek include update status |
| UC24: Manage Projects | UC45: Upload Project Files | Kelola proyek include upload file |
| UC24: Manage Projects | UC46: Add Project Notes | Kelola proyek include catatan |
| UC14: Chat with Admin | UC48: Send Message | Chat include kirim pesan |
| UC14: Chat with Admin | UC50: View Message History | Chat include history |
| UC29: Admin Chat Management | UC48: Send Message | Admin chat include kirim pesan |
| UC29: Admin Chat Management | UC50: View Message History | Admin chat include history |
| UC31: Create Portfolio | UC34: Upload Portfolio Images | Buat portfolio include upload |
| UC32: Edit Portfolio | UC34: Upload Portfolio Images | Edit portfolio include upload |

## 🔄 Extend Relationships

Extend relationships menunjukkan use case yang **mungkin** dijalankan sebagai ekstensi dari use case lain:

| Base Use Case | Extended Use Case | Condition |
|---------------|-------------------|-----------|
| UC38: Review Inquiry | UC39: Accept Inquiry | Jika inquiry diterima |
| UC38: Review Inquiry | UC40: Reject Inquiry | Jika inquiry ditolak |
| UC39: Accept Inquiry | UC41: Convert to Project | Jika inquiry dikonversi |
| UC41: Convert to Project | UC43: Create Project | Jika dibuat proyek baru |
| UC43: Create Project | UC47: Create Contract | Jika dibuatkan kontrak |
| UC11: View Dashboard | UC20: View Notifications | Jika ada notifikasi |
| UC21: Access Admin Dashboard | UC27: View Analytics | Jika melihat analytics |

## 📱 Feature Packages

### 🌐 Public Features (Guest Access)
- **UC1**: Browse Homepage - Melihat halaman utama
- **UC2**: View Portfolio Gallery - Galeri portfolio
- **UC3**: View Portfolio Detail - Detail portfolio
- **UC4**: Submit Contact Form - Form kontak
- **UC5**: Submit Inquiry - Ajukan permintaan
- **UC6**: Register Account - Daftar akun
- **UC7**: Login to System - Login sistem
- **UC8**: View About Page - Halaman tentang

### 👤 Customer Features
- **UC10**: Manage Profile - Kelola profil
- **UC11**: View Dashboard - Dashboard customer
- **UC12**: Track My Projects - Lacak proyek
- **UC13**: View My Inquiries - Lihat inquiry
- **UC14**: Chat with Admin - Chat dengan admin
- **UC15**: View My Contracts - Lihat kontrak
- **UC16**: Download Contract PDF - Download kontrak
- **UC17**: Update Profile Info - Update profil
- **UC18**: Change Password - Ubah password
- **UC19**: View Project Progress - Lihat progress
- **UC20**: View Notifications - Lihat notifikasi

### 🔧 Admin Management
- **UC21**: Access Admin Dashboard - Dashboard admin
- **UC22**: Manage Customers - Kelola customer
- **UC23**: Manage Inquiries - Kelola inquiry
- **UC24**: Manage Projects - Kelola proyek
- **UC25**: Manage Contracts - Kelola kontrak
- **UC26**: Manage Services - Kelola layanan
- **UC27**: View Analytics - Lihat analytics
- **UC28**: Generate Reports - Generate laporan
- **UC29**: Admin Chat Management - Kelola chat

### 🎨 Portfolio Management
- **UC31**: Create Portfolio - Buat portfolio
- **UC32**: Edit Portfolio - Edit portfolio
- **UC33**: Delete Portfolio - Hapus portfolio
- **UC34**: Upload Portfolio Images - Upload gambar
- **UC35**: Set Featured Portfolio - Set featured

### 📋 Inquiry Management
- **UC38**: Review Inquiry - Review inquiry
- **UC39**: Accept Inquiry - Terima inquiry
- **UC40**: Reject Inquiry - Tolak inquiry
- **UC41**: Convert to Project - Konversi ke proyek

### 🏗️ Project Management
- **UC43**: Create Project - Buat proyek
- **UC44**: Update Project Status - Update status
- **UC45**: Upload Project Files - Upload file
- **UC46**: Add Project Notes - Tambah catatan
- **UC47**: Create Contract - Buat kontrak

### 💬 Communication System
- **UC48**: Send Message - Kirim pesan
- **UC49**: Receive Message - Terima pesan
- **UC50**: View Message History - Lihat history
- **UC51**: Send Notification - Kirim notifikasi
- **UC52**: Mark as Read - Tandai dibaca

## 🏗️ System Architecture Mapping

### Controllers Mapping
```php
// Public Features
HomeController::class           // UC1, UC2, UC3, UC8
ContactController::class        // UC4
InquiryController::class        // UC5
Auth\RegisterController::class  // UC6
Auth\LoginController::class     // UC7

// Customer Features
Customer\DashboardController::class  // UC11, UC20
Customer\ProfileController::class    // UC10, UC17, UC18
Customer\ProjectController::class    // UC12, UC19
Customer\InquiryController::class    // UC13
Customer\ContractController::class   // UC15, UC16
MessageController::class            // UC14, UC48-UC52

// Admin Features
Admin\DashboardController::class     // UC21, UC27
Admin\CustomerController::class      // UC22
Admin\InquiryController::class       // UC23, UC38-UC41
Admin\ProjectController::class       // UC24, UC43-UC47
Admin\ContractController::class      // UC25
Admin\ServiceController::class       // UC26
Admin\ReportsController::class       // UC28
Admin\MessageController::class       // UC29, UC48-UC52
Admin\PortfolioController::class     // UC31-UC35
```

### Database Models
```php
User::class         // Guest, Customer, Admin actors
Service::class      // UC26 - Service management
Portfolio::class    // UC2, UC3, UC31-UC35
Inquiry::class      // UC5, UC13, UC23, UC38-UC41
Project::class      // UC12, UC19, UC24, UC43-UC47
Contract::class     // UC15, UC16, UC25, UC47
Message::class      // UC14, UC29, UC48-UC52
Chat::class         // Communication system
Notification::class // UC20, UC51, UC52
```

## 🔄 Business Process Flow

### 1. **Customer Journey**
```
Guest → Register (UC6) → Login (UC7) → Dashboard (UC11) → Submit Inquiry (UC5) → Track Project (UC12) → View Contract (UC15)
```

### 2. **Admin Workflow**
```
Login → Dashboard (UC21) → Review Inquiry (UC38) → Accept/Reject (UC39/UC40) → Convert to Project (UC41) → Create Contract (UC47)
```

### 3. **Communication Flow**
```
Customer Chat (UC14) ↔ Admin Chat (UC29) → Send/Receive Messages (UC48/UC49) → Notifications (UC51)
```

## 📈 Key Features

### ✅ **Comprehensive Coverage**
- Semua fitur website tercakup dalam use case
- Include dan extend relationships yang logis
- Mapping yang jelas ke arsitektur Laravel

### ✅ **Role-Based Access**
- Guest: Akses publik dan registrasi
- Customer: Manajemen proyek pribadi
- Admin: Manajemen sistem lengkap

### ✅ **Real-time Communication**
- Chat system antara customer dan admin
- Notification system untuk update status
- Message history dan read status

### ✅ **Project Lifecycle**
- Inquiry → Project → Contract flow
- Status tracking dan progress monitoring
- File management dan documentation

## 📁 Files Generated

1. **use-case-diagram-complete.puml** - PlantUML diagram source
2. **deskripsi-use-case-complete.md** - Detailed use case descriptions
3. **USE_CASE_SUMMARY.md** - This summary document

## 🔧 Usage Instructions

1. **View Diagram**: Open `.puml` file in PlantUML viewer
2. **Implementation**: Use use case descriptions as requirements
3. **Testing**: Validate implementation against use case scenarios
4. **Maintenance**: Update diagram when adding new features

## 📊 Metrics

- **Total Use Cases**: 47
- **Include Relationships**: 12
- **Extend Relationships**: 7
- **Actors**: 3
- **Packages**: 7
- **Controllers Mapped**: 15+
- **Models Involved**: 8+
