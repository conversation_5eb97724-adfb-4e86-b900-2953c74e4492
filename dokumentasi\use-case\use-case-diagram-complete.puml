@startuml Use Case Diagram - ARDFYA v2.1

!theme plain
skinparam handwritten false
skinparam shadowing false
skinparam usecase {
  BackgroundColor #E1F5FE
  BorderColor #0288D1
  ArrowColor #01579B
  ActorBorderColor #01579B
  ActorFontColor #01579B
}

skinparam packageStyle rectangle
skinparam rectangle {
  BackgroundColor white
  BorderColor #BDBDBD
}

title Use Case Diagram - ARDFYA v2.1 Construction Management System

' Actors
actor "Guest" as G #E3F2FD
actor "Customer" as C #E1F5FE
actor "Admin" as A #E8F5E9

' System boundary
rectangle "ARDFYA v2.1 System" {
  
  ' Guest Use Cases
  package "Public Features" {
    usecase "Browse Homepage" as UC1
    usecase "View Portfolio Gallery" as UC2
    usecase "View Portfolio Detail" as UC3
    usecase "Submit Contact Form" as UC4
    usecase "Submit Inquiry" as UC5
    usecase "Register Account" as UC6
    usecase "Login to System" as UC7
    usecase "View About Page" as UC8
  }
  
  ' Customer Use Cases
  package "Customer Features" {
    usecase "Manage Profile" as UC10
    usecase "View Dashboard" as UC11
    usecase "Track My Projects" as UC12
    usecase "View My Inquiries" as UC13
    usecase "Chat with Admin" as UC14
    usecase "View My Contracts" as UC15
    usecase "Download Contract PDF" as UC16
    usecase "Update Profile Info" as UC17
    usecase "Change Password" as UC18
    usecase "View Project Progress" as UC19
    usecase "View Notifications" as UC20
  }
  
  ' Admin Use Cases
  package "Admin Management" {
    usecase "Access Admin Dashboard" as UC21
    usecase "Manage Customers" as UC22
    usecase "Manage Inquiries" as UC23
    usecase "Manage Projects" as UC24
    usecase "Manage Contracts" as UC25
    usecase "Manage Services" as UC26
    usecase "View Analytics" as UC27
    usecase "Generate Reports" as UC28
    usecase "Admin Chat Management" as UC29
  }
  
  ' Portfolio Management
  package "Portfolio Management" {
    usecase "Create Portfolio" as UC31
    usecase "Edit Portfolio" as UC32
    usecase "Delete Portfolio" as UC33
    usecase "Upload Portfolio Images" as UC34
    usecase "Set Featured Portfolio" as UC35
  }
  
  ' Inquiry Management
  package "Inquiry Management" {
    usecase "Review Inquiry" as UC38
    usecase "Accept Inquiry" as UC39
    usecase "Reject Inquiry" as UC40
    usecase "Convert to Project" as UC41
  }
  
  ' Project Management
  package "Project Management" {
    usecase "Create Project" as UC43
    usecase "Update Project Status" as UC44
    usecase "Upload Project Files" as UC45
    usecase "Add Project Notes" as UC46
    usecase "Create Contract" as UC47
  }
  
  ' Communication
  package "Communication System" {
    usecase "Send Message" as UC48
    usecase "Receive Message" as UC49
    usecase "View Message History" as UC50
    usecase "Send Notification" as UC51
    usecase "Mark as Read" as UC52
  }
}

' Guest relationships
G --> UC1
G --> UC2
G --> UC3
G --> UC4
G --> UC5
G --> UC6
G --> UC7
G --> UC8

' Customer relationships
C --> UC10
C --> UC11
C --> UC12
C --> UC13
C --> UC14
C --> UC15
C --> UC17
C --> UC18
C --> UC19
C --> UC20
C --> UC5

' Admin relationships
A --> UC21
A --> UC22
A --> UC23
A --> UC24
A --> UC25
A --> UC26
A --> UC27
A --> UC28
A --> UC29
A --> UC31
A --> UC32
A --> UC33
A --> UC34
A --> UC35
A --> UC38
A --> UC39
A --> UC40
A --> UC41
A --> UC43
A --> UC44
A --> UC45
A --> UC46
A --> UC47
A --> UC48

' Include relationships
UC6 ..> UC7 : <<include>>
UC15 ..> UC16 : <<include>>
UC23 ..> UC38 : <<include>>
UC24 ..> UC44 : <<include>>
UC24 ..> UC45 : <<include>>
UC24 ..> UC46 : <<include>>
UC14 ..> UC48 : <<include>>
UC14 ..> UC50 : <<include>>
UC29 ..> UC48 : <<include>>
UC29 ..> UC50 : <<include>>
UC31 ..> UC34 : <<include>>
UC32 ..> UC34 : <<include>>

' Extend relationships
UC39 ..> UC41 : <<extend>>
UC41 ..> UC43 : <<extend>>
UC43 ..> UC47 : <<extend>>
UC38 ..> UC39 : <<extend>>
UC38 ..> UC40 : <<extend>>
UC11 ..> UC20 : <<extend>>
UC21 ..> UC27 : <<extend>>

@enduml
