@startuml Use Case Diagram - ARDFYA v2.1

!theme plain
skinparam handwritten false
skinparam shadowing false

' Styling untuk Use Case
skinparam usecase {
    BackgroundColor #E1F5FE
    BorderColor #0288D1
    BorderThickness 2
    FontSize 11
    FontStyle bold
}

' Styling untuk Actor
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #01579B
    BorderThickness 2
    FontSize 12
    FontStyle bold
}

' Styling untuk Package
skinparam package {
    BackgroundColor #F5F5F5
    BorderColor #757575
    BorderThickness 2
    FontSize 12
    FontStyle bold
}

' Styling untuk Rectangle
skinparam rectangle {
    BackgroundColor #FAFAFA
    BorderColor #424242
    BorderThickness 3
    FontSize 14
    FontStyle bold
}

' Arrow styling
skinparam arrow {
    Color #01579B
    Thickness 2
}

title **Use Case Diagram - ARDFYA v2.1**\n//Construction Management System//

' ===== ACTORS =====
actor "🌐 Guest" as G
actor "👤 Customer" as C
actor "⚙️ Admin" as A

' ===== SYSTEM BOUNDARY =====
rectangle "**ARDFYA v2.1 System**" as MainSystem {

    ' ===== PUBLIC FEATURES =====
    package "🌐 **Public Features**" as PublicPkg {
        usecase "🏠 Browse Homepage" as UC01
        usecase "🖼️ View Portfolio Gallery" as UC02
        usecase "📋 View Portfolio Detail" as UC03
        usecase "📧 Submit Contact Form" as UC04
        usecase "📝 Submit Inquiry" as UC05
        usecase "👤 Register Account" as UC06
        usecase "🔐 Login to System" as UC07
        usecase "ℹ️ View About Page" as UC08
    }

    ' ===== CUSTOMER FEATURES =====
    package "👤 **Customer Features**" as CustomerPkg {
        usecase "⚙️ Manage Profile" as UC10
        usecase "📊 View Dashboard" as UC11
        usecase "📈 Track My Projects" as UC12
        usecase "📋 View My Inquiries" as UC13
        usecase "💬 Chat with Admin" as UC14
        usecase "📄 View My Contracts" as UC15
        usecase "⬇️ Download Contract PDF" as UC16
        usecase "✏️ Update Profile Info" as UC17
        usecase "🔑 Change Password" as UC18
        usecase "📊 View Project Progress" as UC19
        usecase "🔔 View Notifications" as UC20
    }

    ' ===== ADMIN MANAGEMENT =====
    package "⚙️ **Admin Management**" as AdminPkg {
        usecase "🏠 Access Admin Dashboard" as UC21
        usecase "👥 Manage Customers" as UC22
        usecase "📋 Manage Inquiries" as UC23
        usecase "🏗️ Manage Projects" as UC24
        usecase "📄 Manage Contracts" as UC25
        usecase "🛠️ Manage Services" as UC26
        usecase "📊 View Analytics" as UC27
        usecase "📈 Generate Reports" as UC28
        usecase "💬 Admin Chat Management" as UC29
    }

    ' ===== PORTFOLIO MANAGEMENT =====
    package "🎨 **Portfolio Management**" as PortfolioPkg {
        usecase "➕ Create Portfolio" as UC31
        usecase "✏️ Edit Portfolio" as UC32
        usecase "🗑️ Delete Portfolio" as UC33
        usecase "📷 Upload Portfolio Images" as UC34
        usecase "⭐ Set Featured Portfolio" as UC35
    }

    ' ===== INQUIRY MANAGEMENT =====
    package "📝 **Inquiry Management**" as InquiryPkg {
        usecase "👁️ Review Inquiry" as UC38
        usecase "✅ Accept Inquiry" as UC39
        usecase "❌ Reject Inquiry" as UC40
        usecase "🔄 Convert to Project" as UC41
    }

    ' ===== PROJECT MANAGEMENT =====
    package "🏗️ **Project Management**" as ProjectPkg {
        usecase "➕ Create Project" as UC43
        usecase "🔄 Update Project Status" as UC44
        usecase "📤 Upload Project Files" as UC45
        usecase "📝 Add Project Notes" as UC46
        usecase "📄 Create Contract" as UC47
    }

    ' ===== COMMUNICATION SYSTEM =====
    package "💬 **Communication System**" as CommPkg {
        usecase "📤 Send Message" as UC48
        usecase "📥 Receive Message" as UC49
        usecase "📋 View Message History" as UC50
        usecase "🔔 Send Notification" as UC51
        usecase "✓ Mark as Read" as UC52
    }
}

' ===== GUEST RELATIONSHIPS =====
G --> UC01 : accesses
G --> UC02 : views
G --> UC03 : views
G --> UC04 : submits
G --> UC05 : submits
G --> UC06 : registers
G --> UC07 : logs in
G --> UC08 : views

' ===== CUSTOMER RELATIONSHIPS =====
C --> UC10 : manages
C --> UC11 : views
C --> UC12 : tracks
C --> UC13 : views
C --> UC14 : communicates
C --> UC15 : views
C --> UC17 : updates
C --> UC18 : changes
C --> UC19 : views
C --> UC20 : views
C --> UC05 : submits

' ===== ADMIN RELATIONSHIPS =====
A --> UC21 : accesses
A --> UC22 : manages
A --> UC23 : manages
A --> UC24 : manages
A --> UC25 : manages
A --> UC26 : manages
A --> UC27 : views
A --> UC28 : generates
A --> UC29 : manages

' Admin Portfolio Management
A --> UC31 : creates
A --> UC32 : edits
A --> UC33 : deletes
A --> UC34 : uploads
A --> UC35 : sets

' Admin Inquiry Management
A --> UC38 : reviews
A --> UC39 : accepts
A --> UC40 : rejects
A --> UC41 : converts

' Admin Project Management
A --> UC43 : creates
A --> UC44 : updates
A --> UC45 : uploads
A --> UC46 : adds
A --> UC47 : creates

' Admin Communication
A --> UC48 : sends

' ===== INCLUDE RELATIONSHIPS =====
' Include means "always includes" (mandatory)
UC06 ..> UC07 : <<include>>\n//registration includes login//
UC15 ..> UC16 : <<include>>\n//viewing contracts includes download//
UC23 ..> UC38 : <<include>>\n//managing inquiries includes review//
UC24 ..> UC44 : <<include>>\n//managing projects includes status updates//
UC24 ..> UC45 : <<include>>\n//managing projects includes file uploads//
UC24 ..> UC46 : <<include>>\n//managing projects includes notes//
UC14 ..> UC48 : <<include>>\n//chat includes sending messages//
UC14 ..> UC50 : <<include>>\n//chat includes viewing history//
UC29 ..> UC48 : <<include>>\n//admin chat includes sending messages//
UC29 ..> UC50 : <<include>>\n//admin chat includes viewing history//
UC31 ..> UC34 : <<include>>\n//creating portfolio includes uploading images//
UC32 ..> UC34 : <<include>>\n//editing portfolio includes uploading images//

' ===== EXTEND RELATIONSHIPS =====
' Extend means "may include" (optional)
UC39 ..> UC41 : <<extend>>\n//accepting inquiry may lead to conversion//
UC41 ..> UC43 : <<extend>>\n//conversion may lead to project creation//
UC43 ..> UC47 : <<extend>>\n//project creation may lead to contract creation//
UC38 ..> UC39 : <<extend>>\n//review may lead to acceptance//
UC38 ..> UC40 : <<extend>>\n//review may lead to rejection//
UC11 ..> UC20 : <<extend>>\n//dashboard may show notifications//
UC21 ..> UC27 : <<extend>>\n//admin dashboard may show analytics//

@enduml
