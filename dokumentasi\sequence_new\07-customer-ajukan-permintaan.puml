@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Customer Ajukan Permintaan**

actor Customer
participant "Dashboard" as Dashboard
participant "Database" as DB
participant "Notification" as Notif

Customer -> Dashboard: Klik menu ajukan permintaan
activate Dashboard

Dashboard --> Customer: Tampilkan form permintaan

Customer -> Dashboard: Isi detail permintaan
Customer -> Dashboard: Upload file pendukung (opsional)
Customer -> Dashboard: <PERSON><PERSON> tombol submit

Dashboard -> DB: Validasi data permintaan
activate DB
DB --> Dashboard: Return hasil validasi
deactivate DB

Dashboard -> DB: Simpan data permintaan
activate DB
DB --> Dashboard: Return ID permintaan baru
deactivate DB

Dashboard -> Notif: Kirim notifikasi ke admin
activate Notif
Notif --> Dashboard: Confirm notifikasi terkirim
deactivate Notif

Dashboard --> Customer: Tampilkan konfirmasi berhasil
Dashboard --> Customer: Tampilkan nomor referensi permintaan

Customer -> Customer: Catat nomor referensi
Customer -> Customer: Lihat konfirmasi pengajuan

deactivate Dashboard

@enduml
