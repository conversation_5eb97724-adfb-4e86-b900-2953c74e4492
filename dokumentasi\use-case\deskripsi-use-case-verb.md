# Deskripsi Use Case - ARDFYA (Verb-Based)

## Aktor

### Guest
Pengunjung website yang belum memiliki akun atau belum login.

### Customer
Pelanggan yang sudah terdaftar dan dapat mengakses fitur khusus.

### Admin
Administrator yang mengelola seluruh sistem dan proses bisnis.

## Use Cases

### UC1: Browse Website
**Aktor:** Guest, Customer, Admin  
**Deskripsi:** Menjelajahi website untuk melihat informasi perusahaan dan layanan.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses website ARDFYA
2. Sistem menampilkan halaman beranda dengan informasi perusahaan
3. Aktor dapat melihat layanan yang ditawarkan
4. Aktor dapat navigasi ke halaman lain

### UC2: View Portfolio
**Aktor:** Guest, Customer, Admin  
**Deskripsi:** Melihat portfolio proyek yang telah dikerjakan perusahaan.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses halaman portfolio
2. Sistem menampilkan galeri proyek dengan gambar dan deskripsi
3. Aktor dapat melihat detail setiap proyek
4. Aktor mendapat inspirasi untuk proyek mereka

### UC3: Submit Inquiry
**Aktor:** Guest, Customer  
**Deskripsi:** Mengajukan permintaan layanan konstruksi atau arsitektur.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengisi form inquiry dengan detail kebutuhan
2. Aktor menyertakan informasi kontak dan lokasi proyek
3. Sistem menyimpan inquiry dan memberikan nomor referensi
4. Sistem mengirimkan notifikasi ke admin
5. Aktor menerima konfirmasi pengajuan

### UC4: Register
**Aktor:** Guest  
**Deskripsi:** Mendaftar akun baru untuk menjadi customer.  
**Prasyarat:** -  
**Alur Utama:**
1. Guest mengisi form registrasi (nama, email, password, telepon)
2. Sistem memvalidasi data dan keunikan email
3. Sistem membuat akun customer baru
4. Sistem melakukan login otomatis (include UC5)
5. Customer diarahkan ke dashboard

### UC5: Login
**Aktor:** Guest, Customer, Admin  
**Deskripsi:** Masuk ke sistem dengan kredensial yang valid.  
**Prasyarat:** Memiliki akun terdaftar  
**Alur Utama:**
1. Aktor memasukkan email dan password
2. Sistem memvalidasi kredensial
3. Sistem mengidentifikasi role (customer/admin)
4. Sistem membuat session dan mengarahkan ke dashboard sesuai role

### UC6: Access Dashboard
**Aktor:** Customer  
**Deskripsi:** Mengakses dashboard pribadi untuk melihat ringkasan aktivitas.  
**Prasyarat:** Login sebagai customer  
**Alur Utama:**
1. Customer login ke sistem
2. Sistem menampilkan dashboard dengan ringkasan:
   - Status inquiry yang diajukan
   - Progress proyek yang sedang berjalan
   - Pesan terbaru dari admin
3. Customer dapat navigasi ke fitur lain dari dashboard
4. Dashboard dapat menampilkan tracking proyek (extend UC7)

### UC7: Track Project
**Aktor:** Customer  
**Deskripsi:** Memantau progress proyek yang sedang dikerjakan.  
**Prasyarat:** Login sebagai customer, memiliki proyek aktif  
**Alur Utama:**
1. Customer mengakses halaman tracking proyek
2. Sistem menampilkan status proyek dengan timeline
3. Customer dapat melihat foto progress dan milestone
4. Customer dapat melihat estimasi penyelesaian
5. Customer mendapat update real-time tentang progress

### UC8: Chat
**Aktor:** Customer, Admin  
**Deskripsi:** Berkomunikasi secara real-time untuk koordinasi proyek.  
**Prasyarat:** Login ke sistem  
**Alur Utama:**
1. Aktor mengakses fitur chat
2. Sistem menampilkan history percakapan
3. Aktor mengetik dan mengirim pesan
4. Sistem menyampaikan pesan ke pihak lain secara real-time
5. Kedua pihak dapat berkomunikasi dua arah

### UC9: View Contract
**Aktor:** Customer  
**Deskripsi:** Melihat kontrak proyek yang telah dibuat admin.  
**Prasyarat:** Login sebagai customer, memiliki kontrak  
**Alur Utama:**
1. Customer mengakses halaman kontrak
2. Sistem menampilkan daftar kontrak dengan status
3. Customer memilih kontrak untuk dilihat
4. Sistem menampilkan detail kontrak (nilai, syarat, jadwal)
5. Customer dapat mengunduh dokumen (include UC10)

### UC10: Download Document
**Aktor:** Customer  
**Deskripsi:** Mengunduh dokumen kontrak dalam format PDF.  
**Prasyarat:** UC9 (View Contract)  
**Alur Utama:**
1. Customer memilih opsi download pada kontrak
2. Sistem menghasilkan file PDF dari data kontrak
3. Sistem mengirimkan file ke browser customer
4. Customer menyimpan dokumen di perangkat mereka

### UC11: Update Profile
**Aktor:** Customer  
**Deskripsi:** Memperbarui informasi profil pribadi.  
**Prasyarat:** Login sebagai customer  
**Alur Utama:**
1. Customer mengakses halaman profil
2. Sistem menampilkan form dengan data saat ini
3. Customer mengubah informasi yang diperlukan
4. Sistem memvalidasi perubahan
5. Sistem menyimpan data baru dan memberikan konfirmasi

### UC12: Manage Customer
**Aktor:** Admin  
**Deskripsi:** Mengelola data dan informasi customer.  
**Prasyarat:** Login sebagai admin  
**Alur Utama:**
1. Admin mengakses menu manajemen customer
2. Sistem menampilkan daftar customer dengan informasi dasar
3. Admin dapat melihat detail customer dan riwayat proyek
4. Admin dapat mengedit informasi customer jika diperlukan
5. Admin dapat melihat statistik dan aktivitas customer

### UC13: Process Inquiry
**Aktor:** Admin  
**Deskripsi:** Memproses inquiry yang masuk dari calon customer.  
**Prasyarat:** Login sebagai admin  
**Alur Utama:**
1. Admin mengakses daftar inquiry yang masuk
2. Sistem menampilkan detail inquiry dengan informasi lengkap
3. Admin mengevaluasi kelayakan dan ketersediaan resources
4. Admin memberikan response (terima/tolak/minta info tambahan)
5. Jika diterima, inquiry dapat dikonversi menjadi proyek (extend UC14)
6. Sistem mengirimkan notifikasi ke customer

### UC14: Manage Project
**Aktor:** Admin  
**Deskripsi:** Mengelola proyek dari tahap perencanaan hingga penyelesaian.  
**Prasyarat:** Login sebagai admin  
**Alur Utama:**
1. Admin mengakses menu manajemen proyek
2. Sistem menampilkan daftar proyek dengan status masing-masing
3. Admin dapat mengupdate progress dan milestone proyek
4. Admin dapat mengupload foto dan dokumen progress
5. Admin dapat mengubah timeline dan estimasi
6. Untuk proyek yang selesai, admin dapat membuat kontrak (extend UC16)

### UC15: Create Portfolio
**Aktor:** Admin  
**Deskripsi:** Membuat portfolio baru dari proyek yang telah selesai.  
**Prasyarat:** Login sebagai admin  
**Alur Utama:**
1. Admin mengakses menu portfolio
2. Admin memilih proyek yang akan dijadikan portfolio
3. Admin mengupload foto-foto terbaik dari proyek
4. Admin menulis deskripsi dan detail teknis
5. Admin mengatur kategori dan tag portfolio
6. Sistem menyimpan portfolio dan menampilkan di website

### UC16: Generate Contract
**Aktor:** Admin  
**Deskripsi:** Membuat kontrak resmi untuk proyek yang telah disetujui.  
**Prasyarat:** UC14 (Manage Project) - proyek sudah final  
**Alur Utama:**
1. Admin memilih proyek yang siap dibuatkan kontrak
2. Sistem menampilkan template kontrak dengan data proyek
3. Admin mengisi detail kontrak (nilai, syarat pembayaran, timeline)
4. Admin menambahkan klausul khusus jika diperlukan
5. Sistem menghasilkan dokumen kontrak dalam format PDF
6. Kontrak tersedia untuk dilihat dan diunduh customer

## Relationships

### Include Relationships (Selalu Dilakukan)

#### UC4 → UC5: Register include Login
- Setelah registrasi berhasil, sistem otomatis melakukan login
- Customer tidak perlu login manual setelah register
- Memberikan pengalaman yang seamless

#### UC9 → UC10: View Contract include Download Document
- Ketika customer melihat kontrak, opsi download selalu tersedia
- Download adalah bagian integral dari melihat kontrak
- Customer perlu menyimpan salinan kontrak

### Extend Relationships (Opsional/Kondisional)

#### UC13 → UC14: Process Inquiry extend to Manage Project
- Inquiry yang disetujui admin dapat dikonversi menjadi proyek
- Tidak semua inquiry menjadi proyek (bisa ditolak)
- Konversi terjadi setelah evaluasi dan persetujuan

#### UC14 → UC16: Manage Project extend to Generate Contract
- Proyek yang sudah final dan disetujui customer dapat dibuatkan kontrak
- Tidak semua proyek langsung mendapat kontrak
- Kontrak dibuat setelah kesepakatan detail dan harga

#### UC6 → UC7: Access Dashboard extend to Track Project
- Dashboard dapat menampilkan ringkasan tracking proyek
- Tidak semua customer memiliki proyek aktif
- Tracking hanya muncul jika ada proyek yang sedang berjalan

## Alur Bisnis Utama

### 1. Customer Journey
```
Browse Website → View Portfolio → Submit Inquiry → Register → Login → 
Access Dashboard → Track Project → View Contract → Download Document
```

### 2. Admin Workflow
```
Login → Process Inquiry → Manage Project → Generate Contract → 
Create Portfolio → Chat (support customer)
```

### 3. Communication Flow
```
Customer Chat ↔ Admin Chat (bidirectional real-time communication)
```

## Skenario Pre-Development

Use case ini dirancang untuk skenario sebelum website dibuat, dengan fokus pada:

1. **Kebutuhan Bisnis Dasar**: Inquiry, proyek, kontrak, portfolio
2. **Interaksi Sederhana**: Chat, tracking, download
3. **Proses Bisnis Inti**: Dari inquiry hingga kontrak
4. **User Experience**: Browse, register, dashboard
5. **Admin Operations**: Process, manage, create, generate

Diagram ini dapat digunakan sebagai blueprint untuk pengembangan website ARDFYA dengan fitur-fitur essential yang dibutuhkan bisnis konstruksi dan arsitektur.
