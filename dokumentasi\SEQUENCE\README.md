# 📊 Sequence Diagram - Sistem ARDFYA v2.1

Folder ini berisi sequence diagram yang menggambarkan interaksi antar objek dalam sistem ARDFYA v2.1 berdasarkan activity diagram yang telah dibuat.

## 📁 **Daftar Sequence Diagram**

### 🔐 **Autentikasi & Registrasi**
- `sequence-login.puml` - Proses login pengguna
- `sequence-registration.puml` - Proses registrasi pengguna baru

### 📊 **Manajemen Proyek**
- `sequence-inquiry.puml` - Proses pengajuan inquiry
- `sequence-project-management.puml` - Manajemen proyek
- `sequence-contract.puml` - Pembuatan dan penandatanganan kontrak

### ⚙️ **Admin Workflow**
- `sequence-admin-workflow.puml` - Workflow admin

## 🎨 **Standar Desain**

### ✅ **Desain Sangat Sederhana**
- **Maksimal 3 participant**: <PERSON><PERSON> aktor u<PERSON> (User, Si<PERSON><PERSON>, Admin)
- **Message minimal**: <PERSON>esan yang sangat singkat dan jelas
- **Flow linear**: Urutan yang sangat mudah diikuti
- **No complex interactions**: Tidak ada alt, loop, note, atau interaksi rumit

### ✅ **Prinsip Kesederhanaan**
- **Single responsibility**: Setiap diagram fokus pada 1 proses utama
- **Minimal participants**: Menghindari terlalu banyak objek
- **Direct communication**: Komunikasi langsung tanpa perantara
- **Happy path only**: Hanya menampilkan skenario sukses (tanpa alt/else)
- **No branching**: Tidak ada percabangan atau kondisi

### ✅ **Visual yang Bersih**
- **Styling konsisten**: Warna dan font yang sama dengan activity diagram
- **Bahasa Indonesia**: Semua label menggunakan bahasa Indonesia
- **Layout sederhana**: Tidak ada elemen visual yang rumit
- **Hide footbox**: Menghilangkan garis bawah untuk tampilan lebih bersih
- **Easy to read**: Mudah dibaca dalam sekali pandang

## 📋 **Cara Penggunaan**

1. **Buka file .puml** dengan PlantUML viewer
2. **Generate diagram** untuk melihat hasil visual
3. **Export ke format** yang diinginkan (PNG, SVG, PDF)

## 🔄 **Hubungan dengan Activity Diagram**

Sequence diagram ini dibuat berdasarkan activity diagram yang sudah ada:
- **Login Process** → `sequence-login.puml`
- **Registration Process** → `sequence-registration.puml`
- **Inquiry Process** → `sequence-inquiry.puml`
- **Project Management** → `sequence-project-management.puml`
- **Contract Creation** → `sequence-contract.puml`
- **Admin Workflow** → `sequence-admin-workflow.puml`

## 📝 **Catatan**

- Sequence diagram dibuat sederhana untuk kemudahan pemahaman
- Fokus pada interaksi utama antar sistem
- Tidak mencakup detail implementasi teknis
- Menggunakan bahasa Indonesia untuk konsistensi dokumentasi
