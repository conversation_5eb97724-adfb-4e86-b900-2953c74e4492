@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Admin Login**

actor Admin
participant "Admin Panel" as Panel
participant "Auth System" as Auth
participant "Database" as DB

Admin -> Panel: Akses halaman admin login
activate Panel

Panel --> Admin: Tampilkan form login admin

Admin -> Panel: Input username dan password
Panel -> Auth: Validasi kredensial admin
activate Auth

Auth -> DB: Cek data admin
activate DB
DB --> Auth: Return data admin dan role
deactivate DB

Auth --> Panel: Return hasil validasi


Panel --> Admin: Redirect ke dashboard admin

deactivate Auth
deactivate Panel

@enduml
