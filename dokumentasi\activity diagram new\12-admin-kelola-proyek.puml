@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho
skinparam nodesep 60
skinparam ranksep 80
skinparam minlen 4
skinparam padding 15

title **Activity Diagram - Admin Kelola Proyek**

|Admin|
start
:Akses menu kelola proyek;

|Sistem|
:Load daftar proyek;
:Tampi<PERSON><PERSON> daftar proyek;
:Tampilkan status proyek;

|Admin|
:Lihat daftar proyek;
:<PERSON><PERSON><PERSON> proyek yang akan dikelola;

|Sistem|
:<PERSON><PERSON><PERSON><PERSON> detail proyek;
:<PERSON><PERSON><PERSON><PERSON> timeline proyek;

|Admin|
:<PERSON><PERSON>h menu aksi;

note right
  Tersedia pilihan:
  • Update Progress
  • Edit Detail Proyek
  • Selesaikan Proyek
end note

|Sistem|
:Proses request aksi;

:Tampilkan form sesuai aksi;

|Admin|
:Input data perubahan;

:Konfirmasi aksi;

|Sistem|
:Validasi dan simpan data;

:Update status proyek;

:Kirim notifikasi ke customer;

:Update tampilan daftar proyek;

|Admin|
:Lihat hasil perubahan;

|Sistem|
stop

@enduml
