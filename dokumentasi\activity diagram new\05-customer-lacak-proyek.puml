@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho

title **Activity Diagram - Customer Lacak Proyek**

|Customer|
start
:Akses menu lacak proyek;

|Sistem|
:Load data proyek customer;
:Tampilkan daftar proyek;
:Tampilkan status proyek;

|Customer|
:Lihat daftar proyek;
:Pilih proyek yang ingin dilacak;

|Sistem|
:Load detail proyek;
:Tampilkan timeline proyek;
:<PERSON><PERSON><PERSON><PERSON> progress proyek;
:Tampilkan file kontrak (jika ada);

|Customer|
:Lihat detail progress;
if (Ada kontrak?) then (Ya)
  :Download kontrak PDF;

  |Sistem|
  :Generate file kontrak;
  :Download kontrak;
  stop
else (Tidak)
  |Customer|
  :Lihat status proyek saja;
  :Selesai melihat proyek;

  |Sistem|
  stop
endif

@enduml
