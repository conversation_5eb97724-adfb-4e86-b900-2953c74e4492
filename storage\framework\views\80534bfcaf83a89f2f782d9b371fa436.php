<?php $__env->startSection('title', '<PERSON><PERSON><PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-800"><PERSON><PERSON><PERSON></h1>
    <p class="text-gray-600 mt-2">Ke<PERSON>la dan pantau progress proyek Anda</p>
</div>

    <?php if($projects->count() > 0): ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <!-- Project Header -->
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800"><?php echo e($project->name); ?></h3>
                            <span class="text-xs px-2 py-1 rounded-full 
                                <?php if($project->status === 'completed'): ?> bg-green-100 text-green-800
                                <?php elseif($project->status === 'in_progress'): ?> bg-blue-100 text-blue-800
                                <?php elseif($project->status === 'planning'): ?> bg-yellow-100 text-yellow-800
                                <?php elseif($project->status === 'on_hold'): ?> bg-orange-100 text-orange-800
                                <?php elseif($project->status === 'cancelled'): ?> bg-red-100 text-red-800
                                <?php else: ?> bg-gray-100 text-gray-800
                                <?php endif; ?>">
                                <?php switch($project->status):
                                    case ('planning'): ?> Perencanaan <?php break; ?>
                                    <?php case ('in_progress'): ?> Berlangsung <?php break; ?>
                                    <?php case ('on_hold'): ?> Ditunda <?php break; ?>
                                    <?php case ('completed'): ?> Selesai <?php break; ?>
                                    <?php case ('cancelled'): ?> Dibatalkan <?php break; ?>
                                    <?php default: ?> <?php echo e(ucfirst($project->status)); ?>

                                <?php endswitch; ?>
                            </span>
                        </div>

                        <!-- Service -->
                        <div class="mb-3">
                            <p class="text-sm text-gray-600">
                                <i class="fas fa-cog mr-2"></i>
                                <?php echo e($project->service->name ?? 'N/A'); ?>

                            </p>
                        </div>

                        <!-- Description -->
                        <p class="text-gray-700 text-sm mb-4"><?php echo e(Str::limit($project->description, 100)); ?></p>

                        <!-- Progress Bar -->
                        <?php if($project->progress_percentage !== null): ?>
                            <div class="mb-4">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span><?php echo e($project->progress_percentage); ?>%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: <?php echo e($project->progress_percentage); ?>%"></div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Project Info -->
                        <div class="space-y-2 text-sm text-gray-600">
                            <?php if($project->budget): ?>
                                <div class="flex items-center">
                                    <i class="fas fa-money-bill-wave mr-2 w-4"></i>
                                    <span>Budget: Rp <?php echo e(number_format($project->budget, 0, ',', '.')); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($project->start_date): ?>
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-start mr-2 w-4"></i>
                                    <span>Mulai: <?php echo e($project->start_date->format('d M Y')); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($project->expected_end_date): ?>
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-check mr-2 w-4"></i>
                                    <span>Target: <?php echo e($project->expected_end_date->format('d M Y')); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Project Actions -->
                    <div class="px-6 py-4 bg-gray-50 border-t">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-500">
                                Update: <?php echo e($project->updated_at->diffForHumans()); ?>

                            </span>
                            <a href="<?php echo e(route('customer.projects.detail', $project)); ?>"
                               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm transition-colors">
                                Detail
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            <?php echo e($projects->links()); ?>

        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="max-w-md mx-auto">
                <div class="mb-4">
                    <i class="fas fa-project-diagram text-6xl text-gray-300"></i>
                </div>
                <h3 class="text-xl font-medium text-gray-700 mb-2">Belum Ada Proyek</h3>
                <p class="text-gray-500 mb-6">Anda belum memiliki proyek. Mulai dengan membuat inquiry terlebih dahulu.</p>
                <a href="<?php echo e(route('inquiries.create')); ?>"
                   class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Buat Inquiry Baru
                </a>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Codingan\CURSOR TA\ardfya_v2\resources\views/customer/projects.blade.php ENDPATH**/ ?>