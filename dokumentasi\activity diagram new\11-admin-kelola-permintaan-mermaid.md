# Activity Diagram - Admin Kelola Permin<PERSON>an

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses menu kelola permintaan]
    
    subgraph Admin ["👤 Admin"]
        A1[Akses menu kelola permintaan]
        A2[Lihat daftar inquiry]
        A3[Pilih inquiry yang akan diproses]
        A4[Baca detail permintaan]
        A5[Pilih aksi yang diinginkan]
        A6[Ubah status menjadi diterima]
        A7[Input estimasi waktu dan biaya]
        A8[Ubah status menjadi ditolak]
        A9[Input alasan penolakan]
        A10[Konversi inquiry menjadi proyek]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load daftar inquiry]
        S2[Tampi<PERSON>an daftar permintaan]
        S3[Tampilkan status inquiry]
        S4[Tampilkan detail inquiry]
        S5[Tampilkan file pendukung]
        S6{Aksi yang dipilih?}
        S7[Update status inquiry]
        S8[Kirim notifikasi ke customer]
        S9[Update status inquiry]
        S10[<PERSON><PERSON> notifikasi ke customer]
        S11[Buat data proyek baru]
        S12[Update status inquiry]
        S13[<PERSON><PERSON> notifikasi ke customer]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> A2
    A2 --> A3
    A3 --> S4
    S4 --> S5
    S5 --> A4
    A4 --> A5
    A5 --> S6
    
    %% Decision branches
    S6 -->|Terima Inquiry| A6
    A6 --> A7
    A7 --> S7
    S7 --> S8
    S8 --> End1([Stop])
    
    S6 -->|Tolak Inquiry| A8
    A8 --> A9
    A9 --> S9
    S9 --> S10
    S10 --> End2([Stop])
    
    S6 -->|Proses ke Proyek| A10
    A10 --> S11
    S11 --> S12
    S12 --> S13
    S13 --> End3([Stop])
    
    %% Styling
    classDef adminClass fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5,A6,A7,A8,A9,A10 adminClass
    class S1,S2,S3,S4,S5,S7,S8,S9,S10,S11,S12,S13 sistemClass
    class S6 decisionClass
    class Start,End1,End2,End3 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja admin saat mengelola permintaan (inquiry) dari customer:

### Swimlane Admin:
- **Akses menu kelola permintaan**: Admin mengklik menu kelola permintaan
- **Lihat daftar inquiry**: Admin melihat semua permintaan yang masuk
- **Pilih inquiry yang akan diproses**: Admin memilih inquiry spesifik
- **Baca detail permintaan**: Admin membaca detail dan file pendukung
- **Pilih aksi yang diinginkan**: Admin memilih tindakan yang akan dilakukan
- **Ubah status menjadi diterima**: Admin menerima permintaan
- **Input estimasi waktu dan biaya**: Admin memberikan estimasi proyek
- **Ubah status menjadi ditolak**: Admin menolak permintaan
- **Input alasan penolakan**: Admin memberikan alasan penolakan
- **Konversi inquiry menjadi proyek**: Admin mengkonversi ke proyek aktif

### Swimlane Sistem:
- **Load daftar inquiry**: Sistem memuat semua inquiry dari database
- **Tampilkan daftar permintaan**: Sistem menampilkan daftar dengan status
- **Tampilkan status inquiry**: Sistem menampilkan status setiap inquiry
- **Tampilkan detail inquiry**: Sistem menampilkan detail permintaan
- **Tampilkan file pendukung**: Sistem menampilkan file yang diupload customer
- **Update status inquiry**: Sistem mengupdate status di database
- **Kirim notifikasi ke customer**: Sistem mengirim notifikasi ke customer
- **Buat data proyek baru**: Sistem membuat record proyek baru

### Decision Points:
Admin dapat memilih dari 3 aksi:

1. **Terima Inquiry**:
   - Ubah status menjadi "diterima"
   - Input estimasi waktu dan biaya
   - Kirim notifikasi penerimaan ke customer

2. **Tolak Inquiry**:
   - Ubah status menjadi "ditolak"
   - Input alasan penolakan
   - Kirim notifikasi penolakan ke customer

3. **Proses ke Proyek**:
   - Konversi inquiry menjadi proyek aktif
   - Buat data proyek baru di sistem
   - Update status dan kirim notifikasi

### Karakteristik:
- **Actor**: Admin
- **Trigger**: Admin ingin memproses permintaan customer
- **Precondition**: Ada inquiry yang perlu diproses
- **Postcondition**: Status inquiry diupdate dan customer mendapat notifikasi

### Alur Proses:
1. Admin mengakses menu kelola permintaan
2. Sistem menampilkan daftar inquiry dengan status
3. Admin memilih dan membaca detail inquiry
4. Admin memilih aksi (terima/tolak/proses ke proyek)
5. Sistem update status dan kirim notifikasi ke customer
