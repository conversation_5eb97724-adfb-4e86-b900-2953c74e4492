@extends('layouts.main')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<!-- Enhanced Hero Section -->
<section class="relative bg-cover bg-center h-screen flex items-center justify-center text-center text-white overflow-hidden" style="background-image: linear-gradient(135deg, rgba(21, 128, 61, 0.75), rgba(22, 101, 52, 0.75)), url('https://img.freepik.com/free-photo/beautiful-luxury-outdoor-swimming-pool-hotel-resort_74190-7433.jpg'); background-attachment: fixed; background-size: cover; background-position: center;">
    <!-- Modern Overlay Pattern -->
    <div class="absolute inset-0 bg-gradient-to-br from-green-900/20 via-transparent to-green-800/30"></div>
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);"></div>

    <!-- Floating Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-white/20 rounded-full animate-ping"></div>
        <div class="absolute top-3/4 right-1/4 w-1 h-1 bg-yellow-300/30 rounded-full animate-pulse"></div>
        <div class="absolute top-1/2 right-1/3 w-3 h-3 bg-white/10 rounded-full animate-bounce"></div>
    </div>

    <div class="container mx-auto px-6 relative z-10">
        <div class="max-w-5xl mx-auto">
            <!-- Main Heading -->
            <h1 class="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black mb-6 leading-tight tracking-tight animate-fade-in-up" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                Layanan <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300">Profesional</span><br>
                <span class="text-green-300">ARDFYA</span>
            </h1>

            <!-- Subtitle -->
            <p class="text-lg md:text-xl lg:text-2xl mb-10 max-w-4xl mx-auto opacity-95 animate-fade-in-up delay-400 leading-relaxed" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">
                Solusi lengkap untuk renovasi dan perbaikan rumah Anda dengan kualitas terbaik, teknologi modern, dan harga yang kompetitif
            </p>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up delay-600">
                <a href="#layanan" class="group relative overflow-hidden bg-white text-green-700 px-8 py-4 rounded-full text-lg font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-tools mr-2 group-hover:rotate-12 transition-transform duration-500"></i>
                        Lihat Layanan
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                </a>
                <a href="{{ route('inquiries.create') }}" class="group relative overflow-hidden border-2 border-white text-white px-8 py-4 rounded-full text-lg font-bold hover:bg-white hover:text-green-700 transition-all duration-300 transform hover:scale-105 backdrop-blur-sm">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-phone mr-2 group-hover:animate-bounce"></i>
                        Konsultasi Gratis
                    </span>
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <a href="#layanan" class="flex flex-col items-center text-white/70 hover:text-white transition-colors group">
            <span class="text-xs mb-2 opacity-0 group-hover:opacity-100 transition-opacity">Scroll untuk melihat layanan</span>
            <div class="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
            </div>
        </a>
    </div>
</section>

<!-- Enhanced Services Section -->
<section id="layanan" class="section-padding bg-gradient-to-br from-gray-50 via-white to-green-50/30">
    <div class="container mx-auto px-6">
        <!-- Enhanced Header -->
        <div class="text-center mb-20">
            <div class="inline-flex items-center px-4 py-2 bg-green-100 rounded-full text-green-700 font-semibold mb-6 animate-fade-in-up">
                <i class="fas fa-tools mr-2"></i>
                Layanan Lengkap
            </div>
            <h2 class="text-4xl md:text-5xl lg:text-6xl font-black text-gray-800 mb-6 animate-fade-in-up delay-200">
                Layanan <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-green-800">Profesional</span> Kami
            </h2>
            <div class="w-32 h-1.5 bg-gradient-to-r from-green-600 via-green-500 to-green-700 mx-auto mb-8 rounded-full animate-fade-in-up delay-300"></div>
            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed animate-fade-in-up delay-400">
                Kami menyediakan berbagai layanan renovasi dan perbaikan rumah dengan tim profesional berpengalaman, kualitas terbaik dan harga kompetitif
            </p>
        </div>

        @if($services->count() > 0)
            <!-- Enhanced Service Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
                @foreach($services as $index => $service)
                    <div class="group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden animate-fade-in-up"
                         style="animation-delay: {{ ($index * 0.1) + 0.6 }}s">

                        <!-- Card Background Gradient -->
                        <div class="absolute inset-0 bg-gradient-to-br from-green-50/50 via-transparent to-blue-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                        <!-- Image Container -->
                        <div class="relative overflow-hidden rounded-t-3xl h-64">
                            @if($service->image_path)
                                <img src="{{ asset('storage/' . $service->image_path) }}"
                                     alt="{{ $service->name }}"
                                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                            @else
                                <div class="w-full h-full bg-gradient-to-br from-green-400 via-green-500 to-green-600 flex items-center justify-center relative">
                                    <div class="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
                                    @if($service->icon)
                                        <i class="{{ $service->icon }} text-6xl text-white relative z-10 group-hover:scale-110 transition-transform duration-500"></i>
                                    @else
                                        <i class="fas fa-tools text-6xl text-white relative z-10 group-hover:scale-110 transition-transform duration-500"></i>
                                    @endif
                                </div>
                            @endif

                            <!-- Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            <!-- Floating Badge -->
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-bold text-green-700 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-2 group-hover:translate-y-0">
                                Premium
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                @if($service->icon)
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                        <i class="{{ $service->icon }} text-green-600 text-xl"></i>
                                    </div>
                                @endif
                                <div>
                                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-green-600 transition-colors">
                                        {{ $service->name }}
                                    </h3>
                                    @if($service->category)
                                        <span class="text-sm text-gray-500">{{ $service->category }}</span>
                                    @endif
                                </div>
                            </div>
                            
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                {{ $service->description }}
                            </p>
                            
                            @if($service->price_range)
                                <div class="mb-4">
                                    <span class="text-sm text-gray-500">Kisaran Harga:</span>
                                    <span class="text-green-600 font-semibold ml-1">{{ $service->price_range }}</span>
                                </div>
                            @endif
                            
                            <!-- Action Buttons -->
                            <div class="flex gap-3">
                                <a href="{{ route('services.show', $service) }}"
                                   class="flex-1 group/btn relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-3 rounded-xl font-bold hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-center">
                                    <span class="relative z-10 flex items-center justify-center">
                                        <i class="fas fa-eye mr-2 group-hover/btn:scale-110 transition-transform duration-300"></i>
                                        Detail
                                    </span>
                                    <div class="absolute inset-0 bg-white opacity-0 group-hover/btn:opacity-10 transition-opacity duration-300"></div>
                                </a>
                                <a href="{{ route('services.inquire', $service) }}"
                                   class="flex-1 group/btn relative overflow-hidden border-2 border-green-600 text-green-700 px-4 py-3 rounded-xl font-bold hover:bg-green-600 hover:text-white transition-all duration-300 transform hover:scale-105 backdrop-blur-sm text-center">
                                    <span class="relative z-10 flex items-center justify-center">
                                        <i class="fas fa-paper-plane mr-2 group-hover/btn:translate-x-1 transition-transform duration-300"></i>
                                        Inquiry
                                    </span>
                                </a>
                            </div>

                            <!-- Decorative Elements -->
                            <div class="absolute -bottom-2 -right-2 w-20 h-20 bg-gradient-to-br from-green-100 to-green-200 rounded-full opacity-0 group-hover:opacity-30 transition-opacity duration-500 transform scale-0 group-hover:scale-100"></div>
                            <div class="absolute -top-2 -left-2 w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-700 transform scale-0 group-hover:scale-100"></div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-tools text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Belum Ada Layanan</h3>
                <p class="text-gray-600 mb-6">Layanan sedang dalam persiapan. Silakan hubungi kami untuk informasi lebih lanjut.</p>
                <a href="{{ route('contact') }}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    Hubungi Kami
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-16 md:py-24 bg-gray-50">
    <div class="container mx-auto px-6">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Mengapa Memilih Kami?</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Kami berkomitmen memberikan layanan terbaik dengan standar kualitas tinggi
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-award text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Berpengalaman</h3>
                <p class="text-gray-600 text-sm">Tim profesional dengan pengalaman bertahun-tahun</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Bergaransi</h3>
                <p class="text-gray-600 text-sm">Garansi kualitas untuk setiap pekerjaan yang kami lakukan</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-clock text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Tepat Waktu</h3>
                <p class="text-gray-600 text-sm">Menyelesaikan proyek sesuai dengan timeline yang disepakati</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-dollar-sign text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Harga Terjangkau</h3>
                <p class="text-gray-600 text-sm">Harga kompetitif dengan kualitas yang tidak diragukan</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 md:py-24 bg-green-700 text-white">
    <div class="container mx-auto px-6 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">Siap Memulai Proyek Anda?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto">
            Konsultasikan kebutuhan renovasi rumah Anda dengan tim ahli kami. Dapatkan estimasi gratis sekarang!
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('inquiries.create') }}" 
               class="bg-white text-green-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Mulai Konsultasi
            </a>
            <a href="{{ route('contact') }}" 
               class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-700 transition-colors">
                Hubungi Kami
            </a>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
</script>
@endsection
