# 📊 ERD - Sistem ARDFYA v2.1

Folder ini berisi ERD (Entity Relationship Diagram) yang menggambarkan struktur database dan hubungan antar entitas dalam sistem ARDFYA v2.1 dengan konsistensi bahasa yang sesuai dengan Activity dan Sequence Diagram yang telah dibuat.

## 📁 **File ERD**

### 🗂️ **Diagram ERD**
- `class-diagram-main.puml` - ERD sederhana untuk reverse engineering
- `erd-database-existing.puml` - ERD berdasarkan database ardfya_db yang ada

### 📊 **ERD Sederhana (class-diagram-main.puml)**
- **Pengguna** - Data pengguna (role: admin/user)
- **Inquiry** - Permintaan proyek dari User
- **Proyek** - Peker<PERSON><PERSON> yang dikerjakan
- **Kontrak** - Perjanjian kerja
- **Portfolio** - Hasil karya untuk Website Publik
- **Chat** - Komunikasi User-Admin

### 📊 **ERD Database Existing (erd-database-existing.puml)**
- **users** - <PERSON><PERSON> pengguna dengan role customer/admin
- **services** - <PERSON><PERSON><PERSON> yang di<PERSON> (Renovasi, Perbaikan, Desain Interior)
- **inquiries** - Permintaan dari customer dengan detail lengkap
- **projects** - Proyek yang dikerjakan dengan tracking progress
- **contracts** - Kontrak dengan status dan installments
- **chats** - Chat dengan file support dan soft delete
- **portfolios** - Portfolio dengan featured dan ordering
- **messages** - Pesan terkait inquiry dan project

### 🎨 **Desain Reverse Engineering**

### ✅ **Sangat Sederhana**
- **6 entitas utama**: Hanya entitas core yang benar-benar penting
- **Minimal attributes**: Field utama saja (id, nama, status)
- **Simple relationships**: Hubungan dasar tanpa detail teknis
- **High-level view**: Pandangan tingkat tinggi sistem

### ✅ **Focus pada Business Logic**
- **Core entities**: Pengguna, Inquiry, Proyek, Kontrak, Portfolio, Chat
- **Business flow**: Inquiry → Proyek → Kontrak (flow utama)
- **User interaction**: Chat untuk komunikasi
- **Content management**: Portfolio untuk Website Publik

### ✅ **Reverse Engineering Appropriate**
- **Black box approach**: Melihat sistem dari luar
- **Essential data only**: Hanya data yang terlihat user
- **No implementation details**: Tidak ada foreign key detail
- **Business perspective**: Dari sudut pandang business process

## 📋 **Cara Membaca Class Diagram (ERD Style)**

### 🏗️ **Struktur Entitas**
```
┌─────────────────┐
│   Nama Entitas  │  ← Nama tabel/entitas (misal: Pengguna)
├─────────────────┤
│ - id: Integer   │  ← Primary key (ID unik)
│ - nama: String  │  ← Data yang disimpan
│ - email: String │
│ - status: String│
└─────────────────┘
```

### 🔗 **Hubungan Antar Entitas**
- **||--o{** → Satu ke banyak (One to Many)
- **||--||** → Satu ke satu (One to One)
- **}o--o{** → Banyak ke banyak (Many to Many)

### 📝 **Contoh Mudah Dipahami**
```
Pengguna ||--o{ Inquiry
```
Artinya: "Satu pengguna bisa membuat banyak inquiry"

```
Kontrak ||--|| Proyek
```
Artinya: "Satu kontrak menghasilkan satu proyek"

## 🎯 **Tujuan ERD**

### ✅ **Untuk Stakeholder**
- **Memahami struktur data**: Apa saja data yang disimpan sistem
- **Melihat hubungan**: Bagaimana data saling terhubung sesuai use case
- **Validasi requirement**: Memastikan ERD sesuai dengan Activity & Sequence Diagram
- **Planning database**: Blueprint database yang siap implementasi

### ✅ **Untuk Tim Development**
- **Database schema**: Panduan langsung untuk CREATE TABLE statements
- **Foreign key relationships**: Relasi yang jelas antar tabel
- **Data types & constraints**: Field specification yang detail
- **Consistent naming**: Nama yang konsisten dengan diagram lainnya

## 📊 **Keunggulan ERD Style**

### ✅ **Sederhana dan Praktis**
- **Satu diagram lengkap**: Semua entitas dalam satu tempat
- **Hubungan jelas**: Relasi antar tabel mudah dipahami
- **Database ready**: Bisa langsung diimplementasi ke database
- **Maintenance friendly**: Mudah diupdate dan dimodifikasi

### ✅ **Business Focused**
- **Bahasa Indonesia**: Semua nama dalam bahasa Indonesia
- **Business terms**: Menggunakan istilah bisnis yang familiar
- **Real-world mapping**: Sesuai dengan proses bisnis nyata
- **Stakeholder friendly**: Mudah dipahami non-technical person

## 📝 **Konsistensi dengan Diagram Lainnya**

### ✅ **Naming Convention**
- **Actor names**: Guest, User, Admin (sama dengan Activity & Sequence)
- **Use case names**: Lihat Beranda, Ajukan Inquiry, dll (Title Case)
- **Module names**: Website Publik, Website Pelanggan, Panel Admin
- **Entity names**: Bahasa Indonesia konsisten

### ✅ **Business Process Mapping (Sederhana)**
- **Lihat Portfolio** → Portfolio
- **Ajukan Inquiry** → Inquiry
- **Lacak Proyek** → Proyek
- **Chat** → Chat
- **Buat Kontrak** → Kontrak
- **Kelola (semua)** → Pengguna (role: admin)

### ✅ **Reverse Engineering Focus**
- **Essential fields only**: id, nama, status (core fields)
- **Simple data types**: String, Integer, Date, DateTime
- **Business status**: Draft, Active, Completed, dll
- **User roles**: admin/user (simple role system)
- **Minimal complexity**: Mudah dipahami stakeholder

## 🔄 **Hubungan dengan Diagram Lain**

ERD ini 100% konsisten dengan:
- **Use Case Diagram**: Semua use case tercermin dalam entitas
- **Activity Diagram**: Setiap aktivitas memiliki data yang sesuai
- **Sequence Diagram**: Objek yang berinteraksi ada dalam ERD
- **Business Process**: Flow bisnis tercermin dalam relasi tabel
