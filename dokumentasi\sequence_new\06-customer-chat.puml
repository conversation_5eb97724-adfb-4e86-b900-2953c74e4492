@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Customer Chat**

actor Customer
participant "Chat System" as Chat
participant "Database" as DB
participant "Notification" as Notif

Customer -> Chat: Klik menu chat
activate Chat

Chat -> DB: Load riwayat chat customer
activate DB
DB --> Chat: Return history chat
deactivate DB

Chat --> Customer: Tampilkan interface chat
Chat --> Customer: <PERSON><PERSON>lkan riwayat percakapan

Customer -> Chat: Ketik pesan baru
Customer -> Chat: Klik tombol kirim

Chat -> DB: Simpan pesan customer
activate DB
DB --> Chat: Confirm pesan tersimpan
deactivate DB

Chat -> Notif: Kirim notifikasi ke admin
activate Notif
Notif --> Chat: Confirm notifikasi terkirim
deactivate Notif

Chat --> Customer: Tampilkan pesan terkirim
Chat --> Customer: Update status pesan

Customer -> Customer: Lihat konfirmasi pesan terkirim

deactivate Chat

@enduml
