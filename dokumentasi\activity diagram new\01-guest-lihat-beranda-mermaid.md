# Activity Diagram - Guest <PERSON><PERSON> Beranda

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses website ARDFYA]
    
    subgraph Guest ["👤 Guest"]
        A1[Akses website ARDFYA]
        A2[Klik menu beranda]
        A3[Lihat informasi beranda]
        A4[Ba<PERSON> konten website]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[<PERSON><PERSON> halaman beranda]
        S2[Tampilkan informasi perusahaan]
        S3[<PERSON><PERSON><PERSON><PERSON> layanan]
        S4[<PERSON><PERSON><PERSON><PERSON> kontak]
    end
    
    %% Flow connections
    A1 --> A2
    A2 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4
    S4 --> A3
    A3 --> A4
    A4 --> End([Stop])
    
    %% Styling
    classDef guestClass fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4 guestClass
    class S1,S2,S3,S4 sistemClass
    class Start,End startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja guest saat mengakses dan melihat halaman beranda website ARDFYA:

### Swimlane Guest:
- **Akses website ARDFYA**: Guest mengunjungi website
- **Klik menu beranda**: Guest mengklik menu beranda
- **Lihat informasi beranda**: Guest melihat konten beranda
- **Baca konten website**: Guest membaca informasi yang tersedia

### Swimlane Sistem:
- **Load halaman beranda**: Sistem memuat halaman beranda
- **Tampilkan informasi perusahaan**: Sistem menampilkan profil perusahaan
- **Tampilkan layanan**: Sistem menampilkan daftar layanan
- **Tampilkan kontak**: Sistem menampilkan informasi kontak

### Karakteristik:
- **Actor**: Guest (pengunjung website)
- **Trigger**: Guest mengakses website ARDFYA
- **Precondition**: Website dapat diakses
- **Postcondition**: Guest berhasil melihat informasi beranda

### Alur Proses:
1. Guest mengakses website ARDFYA
2. Guest mengklik menu beranda
3. Sistem memuat dan menampilkan konten beranda
4. Guest melihat dan membaca informasi yang tersedia
