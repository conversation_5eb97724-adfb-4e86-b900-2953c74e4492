# Activity Diagram - Admin Login

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses halaman login admin]
    
    subgraph Admin ["👤 Admin"]
        A1[Akses halaman login admin]
        A2[Input email dan password]
        A3[Klik tombol login]
        A4[Lihat dashboard admin]
        A5[Akses menu admin]
        A6[Kembali ke halaman login]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Validasi kredensial admin]
        S2{Kredensial valid?}
        S3[Buat session admin]
        S4[Redirect ke dashboard admin]
        S5[Tampilkan pesan error]
    end
    
    %% Flow connections
    A1 --> A2
    A2 --> A3
    A3 --> S1
    S1 --> S2
    
    %% Decision branches
    S2 -->|Ya| S3
    S3 --> S4
    S4 --> A4
    A4 --> A5
    A5 --> End1([Stop])
    
    S2 -->|Tidak| S5
    S5 --> A6
    A6 --> End2([Stop])
    
    %% Styling
    classDef adminClass fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5,A6 adminClass
    class S1,S3,S4,S5 sistemClass
    class S2 decisionClass
    class Start,End1,End2 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja admin saat melakukan proses login ke sistem ARDFYA:

### Swimlane Admin:
- **Akses halaman login admin**: Admin mengunjungi halaman login khusus admin
- **Input email dan password**: Admin memasukkan kredensial login
- **Klik tombol login**: Admin mengirim form login
- **Lihat dashboard admin**: Admin berhasil masuk ke dashboard (jika valid)
- **Akses menu admin**: Admin dapat menggunakan fitur admin
- **Kembali ke halaman login**: Admin kembali ke login (jika gagal)

### Swimlane Sistem:
- **Validasi kredensial admin**: Sistem memverifikasi email dan password admin
- **Kredensial valid?**: Decision point untuk validasi admin
- **Buat session admin**: Sistem membuat session untuk admin
- **Redirect ke dashboard admin**: Sistem mengarahkan ke dashboard admin
- **Tampilkan pesan error**: Sistem menampilkan pesan kesalahan login

### Decision Points:
- **Kredensial valid?**
  - **Ya**: Buat session → Redirect ke dashboard → Admin akses menu
  - **Tidak**: Tampilkan error → Admin kembali ke login

### Karakteristik:
- **Actor**: Admin
- **Trigger**: Admin ingin mengakses panel administrasi
- **Precondition**: Admin memiliki kredensial yang valid
- **Postcondition**: Admin berhasil login dan akses dashboard atau mendapat pesan error

### Alur Proses:
1. Admin mengakses halaman login khusus admin
2. Admin memasukkan email dan password
3. Sistem memvalidasi kredensial admin
4. Jika valid: buat session dan redirect ke dashboard admin
5. Jika tidak valid: tampilkan error dan kembali ke login

### Perbedaan dengan Customer Login:
- Menggunakan halaman login khusus admin
- Validasi role admin
- Redirect ke dashboard admin (bukan customer)
- Akses ke menu administrasi sistem
