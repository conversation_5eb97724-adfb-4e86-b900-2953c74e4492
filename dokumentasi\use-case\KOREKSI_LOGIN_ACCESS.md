# Koreksi Login Access - Use Case Diagram ARDFYA

## 🎯 Overview

Telah dilakukan koreksi pada use case diagram untuk memperbaiki akses login yang sebelumnya hanya untuk Guest. Sekarang diagram sudah sesuai dengan implementasi website ARDFYA yang sebenarnya.

## ❌ Masalah Sebelumnya

### **Login Access Error:**
```plantuml
' SALAH - <PERSON>ya Guest yang bisa login
G --> UC5 : logs in

' Customer dan <PERSON><PERSON> tidak bisa login (TIDAK LOGIS)
C --> UC6 : views (tanpa login?)
A --> UC12 : manages (tanpa login?)
```

### **Public Features Access Error:**
```plantuml
' SALAH - <PERSON>ya Guest yang bisa akses public features
G --> UC1 : accesses
G --> UC2 : views

' Customer dan Admin tidak bisa akses homepage/portfolio (TIDAK LOGIS)
```

## ✅ Koreksi yang Dilakukan

### **1. Login Access (UC5)**

#### **Sebelum:**
```plantuml
' <PERSON>ya Guest
G --> UC5 : logs in
```

#### **Sesudah:**
```plantuml
' Semua aktor bisa login
G --> UC5 : logs in
C --> UC5 : logs in  
A --> UC5 : logs in
```

### **2. Public Features Access (UC1, UC2)**

#### **Sebelum:**
```plantuml
' Hanya Guest
G --> UC1 : accesses
G --> UC2 : views
```

#### **Sesudah:**
```plantuml
' Semua aktor bisa akses public features
G --> UC1 : accesses
C --> UC1 : accesses
A --> UC1 : accesses

G --> UC2 : views
C --> UC2 : views
A --> UC2 : views
```

### **3. Submit Inquiry Access (UC3)**

#### **Tetap Sama (Sudah Benar):**
```plantuml
' Guest dan Customer bisa submit inquiry
G --> UC3 : submits
C --> UC3 : submits
' Admin tidak perlu submit inquiry (sudah benar)
```

## 🔄 Alur Login yang Benar

### **Guest Login:**
```
Guest → Login (UC5) → Redirect to Customer Dashboard (UC6)
```

### **Customer Login:**
```
Customer → Login (UC5) → Access Customer Dashboard (UC6)
```

### **Admin Login:**
```
Admin → Login (UC5) → Access Admin Features (UC12-UC17)
```

## 📋 Updated Actor Access Matrix

| Use Case | Guest | Customer | Admin | Keterangan |
|----------|-------|----------|-------|------------|
| **UC1: Browse Homepage** | ✅ | ✅ | ✅ | Semua bisa akses |
| **UC2: View Portfolio** | ✅ | ✅ | ✅ | Semua bisa akses |
| **UC3: Submit Inquiry** | ✅ | ✅ | ❌ | Admin tidak perlu submit |
| **UC4: Register Account** | ✅ | ❌ | ❌ | Hanya Guest yang register |
| **UC5: Login** | ✅ | ✅ | ✅ | **SEMUA BISA LOGIN** |
| **UC6: View Dashboard** | ❌ | ✅ | ❌ | Khusus Customer |
| **UC7: Track Projects** | ❌ | ✅ | ❌ | Khusus Customer |
| **UC8: Chat with Admin** | ❌ | ✅ | ❌ | Khusus Customer |
| **UC9: View Contracts** | ❌ | ✅ | ❌ | Khusus Customer |
| **UC10: Download Contract** | ❌ | ✅ | ❌ | Include dari UC9 |
| **UC11: Manage Profile** | ❌ | ✅ | ❌ | Khusus Customer |
| **UC12: Manage Customers** | ❌ | ❌ | ✅ | Khusus Admin |
| **UC13: Manage Inquiries** | ❌ | ❌ | ✅ | Khusus Admin |
| **UC14: Manage Projects** | ❌ | ❌ | ✅ | Khusus Admin |
| **UC15: Manage Portfolio** | ❌ | ❌ | ✅ | Khusus Admin |
| **UC16: Create Contracts** | ❌ | ❌ | ✅ | Khusus Admin |
| **UC17: Chat with Customer** | ❌ | ❌ | ✅ | Khusus Admin |

## 🏗️ Implementasi di Website

### **Login Controller:**
```php
// routes/web.php
Route::post('/login', [LoginController::class, 'login']);

// LoginController.php
public function login(Request $request) {
    // Validasi kredensial
    if (Auth::attempt($credentials)) {
        $user = Auth::user();
        
        // Redirect berdasarkan role
        if ($user->role === 'admin') {
            return redirect()->route('admin.dashboard');
        } else {
            return redirect()->route('customer.dashboard');
        }
    }
}
```

### **Navigation Berdasarkan Role:**
```php
// Layout navigation
@guest
    <a href="{{ route('login') }}">Login</a>
    <a href="{{ route('register') }}">Register</a>
@endguest

@auth
    @if(Auth::user()->role === 'admin')
        <a href="{{ route('admin.dashboard') }}">Admin Dashboard</a>
    @else
        <a href="{{ route('customer.dashboard') }}">Dashboard</a>
    @endif
    <a href="{{ route('logout') }}">Logout</a>
@endauth
```

### **Homepage Access:**
```php
// HomeController.php
public function index() {
    // Semua bisa akses homepage
    $services = Service::all();
    $portfolios = Portfolio::featured()->get();
    
    return view('home.index', compact('services', 'portfolios'));
}
```

## 🔗 Updated Relationships

### **Include Relationships:**
- **UC4 → UC5**: Register Account include Login ✅
- **UC9 → UC10**: View Contracts include Download Contract ✅
- **UC8 → UC17**: Chat with Admin include Chat with Customer ✅

### **Extend Relationships:**
- **UC13 → UC14**: Manage Inquiries extend to Manage Projects ✅
- **UC14 → UC16**: Manage Projects extend to Create Contracts ✅

## 📊 Business Logic

### **User Journey:**

#### **Guest Journey:**
```
Browse Homepage → View Portfolio → Submit Inquiry → Register → Login → Customer Dashboard
```

#### **Customer Journey:**
```
Login → Dashboard → Track Projects → Chat with Admin → View Contracts
```

#### **Admin Journey:**
```
Login → Manage Inquiries → Manage Projects → Create Contracts → Chat with Customer
```

## ✅ Validation

### **Sesuai dengan Website ARDFYA:**
- ✅ **Guest** bisa browse, view portfolio, submit inquiry, register, dan login
- ✅ **Customer** bisa login, akses dashboard, track projects, chat, view contracts
- ✅ **Admin** bisa login, manage semua data, chat dengan customer
- ✅ **Public features** dapat diakses oleh semua aktor
- ✅ **Role-based access** untuk fitur khusus

### **Logical Access Control:**
- ✅ Semua aktor bisa akses public features (homepage, portfolio)
- ✅ Semua aktor bisa login (Guest untuk pertama kali, Customer/Admin untuk akses ulang)
- ✅ Role-specific features hanya untuk aktor yang sesuai
- ✅ Business logic yang masuk akal

## 📁 Files Updated

1. **use-case-diagram-clean.puml** - Updated dengan akses login yang benar
2. **deskripsi-use-case-simple.md** - Updated deskripsi aktor dan use case
3. **KOREKSI_LOGIN_ACCESS.md** - Dokumentasi koreksi ini

## 🎯 Result

Diagram sekarang sudah **sesuai dengan implementasi website ARDFYA** yang sebenarnya:
- 🔐 **Login access** untuk semua aktor
- 🌐 **Public features** dapat diakses semua aktor  
- 👤 **Role-based features** sesuai dengan implementasi
- 🔄 **Logical business flow** yang masuk akal
- ✅ **Konsisten** dengan arsitektur website yang ada
