@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Professional styling
skinparam rectangle {
    BackgroundColor #f8f9fa
    BorderColor #495057
    BorderThickness 2
    FontSize 12
    FontStyle bold
}

skinparam usecase {
    BackgroundColor white
    BorderColor #6c757d
    BorderThickness 1
    FontSize 11
}

skinparam actor {
    BackgroundColor #e9ecef
    BorderColor #495057
    BorderThickness 2
    FontSize 12
    FontStyle bold
}

skinparam arrow {
    Color #495057
    Thickness 2
}

' Flow lurus tanpa melengkung
skinparam linetype ortho

title **Use Case Diagram - Sistem ARDFYA v2.1**

' Layout horizontal dengan modul-modul
left to right direction

' Actors positioned vertically
actor "**Pengunjung**" as Guest
actor "**Pelanggan**" as Customer
actor "**Admin**" as Admin

' Main system boundary
rectangle "**Sistem ARDFYA v2.1**" as MainSystem {

  ' Public module - fitur penting untuk pengunjung
  rectangle "Website Publik" as PublicModule {
    usecase "Lihat Beranda" as ViewHome
    usecase "Lihat Portfolio" as ViewPortfolio
    usecase "Registrasi & Login" as RegisterLogin
  }

  ' Customer module - fitur inti untuk pelanggan
  rectangle "Portal Pelanggan" as CustomerModule {
    usecase "Dashboard" as CustomerDashboard
    usecase "Ajukan Inquiry" as SubmitInquiry
    usecase "Lacak Proyek" as TrackProject
    usecase "Chat & Support" as ChatSupport
  }

  ' Admin module - fitur penting untuk admin
  rectangle "Panel Admin" as AdminModule {
    usecase "Dashboard Admin" as AdminDashboard
    usecase "Kelola Inquiry" as ManageInquiries
    usecase "Kelola Proyek" as ManageProjects
    usecase "Kelola Portfolio" as ManagePortfolio
    usecase "Buat Kontrak" as CreateContract
  }
}

' Guest relationships - akses publik
Guest --> ViewHome
Guest --> ViewPortfolio
Guest --> RegisterLogin

' Customer relationships - fitur pelanggan
Customer --> CustomerDashboard
Customer --> SubmitInquiry
Customer --> TrackProject
Customer --> ChatSupport

' Admin relationships - manajemen sistem
Admin --> AdminDashboard
Admin --> ManageInquiries
Admin --> ManageProjects
Admin --> ManagePortfolio
Admin --> CreateContract

' Business workflow - flow lurus
RegisterLogin --> CustomerDashboard : **setelah login**
SubmitInquiry --> ManageInquiries : **admin proses**
ManageProjects --> CreateContract : **buat kontrak**

' Layout hints for better positioning
Guest -[hidden]down- Customer
Customer -[hidden]down- Admin

PublicModule -[hidden]right- CustomerModule
CustomerModule -[hidden]right- AdminModule

@enduml
