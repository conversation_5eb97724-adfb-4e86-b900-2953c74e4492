# Deskripsi Use Case - ARDFYA v2.1

## Public Features

### UC1: Browse Homepage
**Aktor:** Guest, Customer  
**Deskripsi:** Mengakses dan melihat halaman utama website yang menampilkan layanan, portfolio, dan informasi perusahaan.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses URL website
2. Sistem menampilkan halaman beranda dengan layanan, portfolio, dan informasi perusahaan
3. Aktor dapat melihat dan berinteraksi dengan konten di halaman beranda

### UC2: View Portfolio Gallery
**Aktor:** Guest, Customer  
**Deskripsi:** Melihat galeri portfolio proyek yang telah diselesaikan.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses halaman portfolio
2. Sistem menampilkan daftar portfolio dalam bentuk galeri
3. Aktor dapat melihat dan memfilter portfolio berdasarkan kategori

### UC3: View Portfolio Detail
**Aktor:** Guest, Customer  
**Deskripsi:** Melihat detail dari portfolio proyek tertentu.  
**Prasyarat:** UC2  
**Alur Utama:**
1. Aktor memilih portfolio tertentu dari galeri
2. Sistem menampilkan halaman detail portfolio dengan gambar, deskripsi, dan informasi proyek
3. Aktor dapat melihat detail lengkap portfolio

### UC4: Submit Contact Form
**Aktor:** Guest, Customer  
**Deskripsi:** Mengirimkan pesan melalui form kontak.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses halaman kontak
2. Aktor mengisi form kontak (nama, email, pesan)
3. Aktor mengirimkan form
4. Sistem memvalidasi input
5. Sistem menyimpan pesan dan mengirimkan notifikasi ke admin
6. Sistem menampilkan konfirmasi pengiriman berhasil

### UC5: Submit Inquiry
**Aktor:** Guest, Customer  
**Deskripsi:** Mengajukan permintaan layanan (inquiry).  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses form inquiry
2. Aktor mengisi detail permintaan (layanan, informasi kontak, detail proyek)
3. Aktor mengirimkan form
4. Sistem memvalidasi input
5. Sistem menyimpan inquiry dan mengirimkan notifikasi ke admin
6. Sistem menampilkan konfirmasi pengiriman berhasil

### UC6: Register Account
**Aktor:** Guest  
**Deskripsi:** Mendaftar akun baru sebagai customer.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses halaman registrasi
2. Aktor mengisi form registrasi (nama, email, password)
3. Sistem memvalidasi input
4. Sistem membuat akun baru
5. Sistem melakukan login otomatis (include UC7)
6. Sistem mengarahkan ke dashboard customer

### UC7: Login to System
**Aktor:** Guest  
**Deskripsi:** Masuk ke sistem dengan akun yang sudah terdaftar.  
**Prasyarat:** Memiliki akun terdaftar  
**Alur Utama:**
1. Aktor mengakses halaman login
2. Aktor memasukkan email dan password
3. Sistem memvalidasi kredensial
4. Sistem membuat session dan mengarahkan ke dashboard sesuai role

### UC8: View About Page
**Aktor:** Guest, Customer  
**Deskripsi:** Melihat halaman tentang perusahaan.  
**Prasyarat:** -  
**Alur Utama:**
1. Aktor mengakses halaman about
2. Sistem menampilkan informasi tentang perusahaan, visi, misi, dan tim

## Customer Features

### UC10: Manage Profile
**Aktor:** Customer  
**Deskripsi:** Mengelola profil customer.  
**Prasyarat:** UC7  
**Alur Utama:**
1. Aktor mengakses halaman profil
2. Sistem menampilkan informasi profil customer
3. Aktor dapat melihat dan mengedit informasi profil

### UC11: View Dashboard
**Aktor:** Customer  
**Deskripsi:** Melihat dashboard customer dengan ringkasan proyek, inquiry, dan notifikasi.  
**Prasyarat:** UC7  
**Alur Utama:**
1. Aktor login ke sistem
2. Sistem menampilkan dashboard customer dengan ringkasan proyek, inquiry, dan notifikasi
3. Aktor dapat melihat status proyek dan inquiry

### UC12: Track My Projects
**Aktor:** Customer  
**Deskripsi:** Melacak progress proyek yang sedang berjalan.  
**Prasyarat:** UC7, Memiliki proyek aktif  
**Alur Utama:**
1. Aktor mengakses halaman proyek
2. Sistem menampilkan daftar proyek customer
3. Aktor memilih proyek tertentu
4. Sistem menampilkan detail proyek dengan status, timeline, dan progress

### UC13: View My Inquiries
**Aktor:** Customer  
**Deskripsi:** Melihat daftar dan status inquiry yang telah diajukan.  
**Prasyarat:** UC7, Memiliki inquiry  
**Alur Utama:**
1. Aktor mengakses halaman inquiry
2. Sistem menampilkan daftar inquiry customer dengan status
3. Aktor dapat melihat detail inquiry

### UC14: Chat with Admin
**Aktor:** Customer  
**Deskripsi:** Berkomunikasi dengan admin melalui fitur chat.  
**Prasyarat:** UC7  
**Alur Utama:**
1. Aktor mengakses halaman chat
2. Sistem menampilkan history chat dengan admin
3. Aktor mengirim pesan (include UC48)
4. Sistem menyimpan dan mengirimkan pesan ke admin
5. Aktor dapat melihat history chat (include UC50)

### UC15: View My Contracts
**Aktor:** Customer  
**Deskripsi:** Melihat kontrak proyek.  
**Prasyarat:** UC7, Memiliki kontrak  
**Alur Utama:**
1. Aktor mengakses halaman kontrak
2. Sistem menampilkan daftar kontrak customer
3. Aktor memilih kontrak tertentu
4. Sistem menampilkan detail kontrak
5. Aktor dapat mengunduh kontrak (include UC16)

### UC16: Download Contract PDF
**Aktor:** Customer  
**Deskripsi:** Mengunduh dokumen kontrak dalam format PDF.  
**Prasyarat:** UC15  
**Alur Utama:**
1. Aktor memilih opsi download pada kontrak
2. Sistem menghasilkan file PDF kontrak
3. Sistem mengirimkan file PDF ke browser aktor
4. Aktor menerima file PDF kontrak

### UC17: Update Profile Info
**Aktor:** Customer  
**Deskripsi:** Memperbarui informasi profil.  
**Prasyarat:** UC10  
**Alur Utama:**
1. Aktor mengakses halaman edit profil
2. Sistem menampilkan form edit profil
3. Aktor mengubah informasi profil
4. Sistem memvalidasi input
5. Sistem menyimpan perubahan
6. Sistem menampilkan konfirmasi perubahan berhasil

### UC18: Change Password
**Aktor:** Customer  
**Deskripsi:** Mengubah password akun.  
**Prasyarat:** UC10  
**Alur Utama:**
1. Aktor mengakses halaman ubah password
2. Aktor memasukkan password lama dan password baru
3. Sistem memvalidasi input
4. Sistem menyimpan password baru
5. Sistem menampilkan konfirmasi perubahan berhasil

### UC19: View Project Progress
**Aktor:** Customer  
**Deskripsi:** Melihat progress proyek dengan detail.  
**Prasyarat:** UC12  
**Alur Utama:**
1. Aktor memilih proyek tertentu
2. Sistem menampilkan detail progress proyek dengan timeline, milestone, dan foto
3. Aktor dapat melihat detail progress proyek

### UC20: View Notifications
**Aktor:** Customer  
**Deskripsi:** Melihat notifikasi sistem.  
**Prasyarat:** UC7  
**Alur Utama:**
1. Aktor mengakses notifikasi dari dashboard
2. Sistem menampilkan daftar notifikasi
3. Aktor dapat membaca dan menandai notifikasi sebagai telah dibaca

## Admin Management

### UC21: Access Admin Dashboard
**Aktor:** Admin  
**Deskripsi:** Mengakses dashboard admin dengan ringkasan sistem.  
**Prasyarat:** Login sebagai admin  
**Alur Utama:**
1. Admin login ke sistem
2. Sistem memverifikasi role admin
3. Sistem menampilkan dashboard admin dengan ringkasan data dan statistik
4. Admin dapat melihat analytics sistem (extend UC27)

### UC22: Manage Customers
**Aktor:** Admin  
**Deskripsi:** Mengelola data customer.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu manajemen customer
2. Sistem menampilkan daftar customer
3. Admin dapat melihat, mengedit, dan menghapus data customer

### UC23: Manage Inquiries
**Aktor:** Admin  
**Deskripsi:** Mengelola inquiry yang masuk.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu manajemen inquiry
2. Sistem menampilkan daftar inquiry
3. Admin dapat melihat detail inquiry (include UC38)
4. Admin dapat memproses inquiry (terima/tolak/konversi ke proyek)

### UC24: Manage Projects
**Aktor:** Admin  
**Deskripsi:** Mengelola proyek yang sedang berjalan.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu manajemen proyek
2. Sistem menampilkan daftar proyek
3. Admin dapat melihat, mengedit, dan menghapus proyek
4. Admin dapat mengupdate status proyek (include UC44)
5. Admin dapat mengupload file proyek (include UC45)
6. Admin dapat menambahkan catatan proyek (include UC46)

### UC25: Manage Contracts
**Aktor:** Admin  
**Deskripsi:** Mengelola kontrak proyek.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu manajemen kontrak
2. Sistem menampilkan daftar kontrak
3. Admin dapat membuat, melihat, mengedit, dan menghapus kontrak

### UC26: Manage Services
**Aktor:** Admin  
**Deskripsi:** Mengelola layanan yang ditawarkan.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu manajemen layanan
2. Sistem menampilkan daftar layanan
3. Admin dapat membuat, melihat, mengedit, dan menghapus layanan

### UC27: View Analytics
**Aktor:** Admin  
**Deskripsi:** Melihat analitik dan statistik sistem.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu analytics
2. Sistem menampilkan dashboard analytics dengan grafik dan statistik
3. Admin dapat melihat dan memfilter data analytics

### UC28: Generate Reports
**Aktor:** Admin  
**Deskripsi:** Menghasilkan laporan sistem.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu laporan
2. Admin memilih jenis laporan dan parameter
3. Sistem menghasilkan laporan sesuai parameter
4. Admin dapat mengunduh atau mencetak laporan

### UC29: Admin Chat Management
**Aktor:** Admin  
**Deskripsi:** Mengelola chat dengan customer.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu chat
2. Sistem menampilkan daftar chat dengan customer
3. Admin memilih chat tertentu
4. Sistem menampilkan history chat dengan customer
5. Admin dapat mengirim pesan (include UC48)
6. Admin dapat melihat history chat (include UC50)

## Portfolio Management

### UC31: Create Portfolio
**Aktor:** Admin  
**Deskripsi:** Membuat portfolio baru.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu portfolio
2. Admin memilih opsi tambah portfolio
3. Admin mengisi detail portfolio (judul, deskripsi, kategori)
4. Admin mengupload gambar portfolio (include UC34)
5. Sistem menyimpan portfolio baru
6. Sistem menampilkan konfirmasi pembuatan berhasil

### UC32: Edit Portfolio
**Aktor:** Admin  
**Deskripsi:** Mengedit portfolio yang ada.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu portfolio
2. Admin memilih portfolio untuk diedit
3. Admin mengubah detail portfolio
4. Admin dapat mengupload gambar baru (include UC34)
5. Sistem menyimpan perubahan
6. Sistem menampilkan konfirmasi perubahan berhasil

### UC33: Delete Portfolio
**Aktor:** Admin  
**Deskripsi:** Menghapus portfolio.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu portfolio
2. Admin memilih portfolio untuk dihapus
3. Sistem meminta konfirmasi penghapusan
4. Admin mengkonfirmasi penghapusan
5. Sistem menghapus portfolio dan file terkait
6. Sistem menampilkan konfirmasi penghapusan berhasil

### UC34: Upload Portfolio Images
**Aktor:** Admin  
**Deskripsi:** Mengupload gambar untuk portfolio.  
**Prasyarat:** UC31 atau UC32  
**Alur Utama:**
1. Admin memilih opsi upload gambar
2. Admin memilih file gambar dari perangkat
3. Sistem memvalidasi file (format, ukuran)
4. Sistem mengupload dan menyimpan gambar
5. Sistem menampilkan preview gambar

### UC35: Set Featured Portfolio
**Aktor:** Admin  
**Deskripsi:** Mengatur portfolio untuk ditampilkan di halaman utama.  
**Prasyarat:** UC21  
**Alur Utama:**
1. Admin mengakses menu portfolio
2. Admin memilih portfolio untuk dijadikan featured
3. Admin mengaktifkan status featured
4. Sistem menyimpan perubahan
5. Sistem menampilkan portfolio di halaman utama

## Inquiry Management

### UC38: Review Inquiry
**Aktor:** Admin  
**Deskripsi:** Mereview detail inquiry yang masuk.  
**Prasyarat:** UC23  
**Alur Utama:**
1. Admin memilih inquiry untuk direview
2. Sistem menampilkan detail inquiry
3. Admin mereview informasi inquiry
4. Admin dapat menerima inquiry (extend UC39)
5. Admin dapat menolak inquiry (extend UC40)

### UC39: Accept Inquiry
**Aktor:** Admin  
**Deskripsi:** Menerima inquiry dari customer.  
**Prasyarat:** UC38  
**Alur Utama:**
1. Admin memilih opsi terima inquiry
2. Admin mengisi estimasi waktu dan biaya
3. Sistem mengupdate status inquiry menjadi diterima
4. Sistem mengirim notifikasi ke customer
5. Admin dapat mengkonversi inquiry ke proyek (extend UC41)

### UC40: Reject Inquiry
**Aktor:** Admin  
**Deskripsi:** Menolak inquiry dari customer.  
**Prasyarat:** UC38  
**Alur Utama:**
1. Admin memilih opsi tolak inquiry
2. Admin mengisi alasan penolakan
3. Sistem mengupdate status inquiry menjadi ditolak
4. Sistem mengirim notifikasi ke customer

### UC41: Convert to Project
**Aktor:** Admin  
**Deskripsi:** Mengkonversi inquiry menjadi proyek.  
**Prasyarat:** UC39  
**Alur Utama:**
1. Admin memilih opsi konversi ke proyek
2. Sistem menampilkan form pembuatan proyek dengan data dari inquiry
3. Admin mengisi detail tambahan proyek
4. Sistem membuat proyek baru (extend UC43)
5. Sistem mengupdate status inquiry
6. Sistem mengirim notifikasi ke customer

## Project Management

### UC43: Create Project
**Aktor:** Admin  
**Deskripsi:** Membuat proyek baru.  
**Prasyarat:** UC21 atau UC41  
**Alur Utama:**
1. Admin mengakses menu proyek
2. Admin memilih opsi tambah proyek
3. Admin mengisi detail proyek (judul, deskripsi, timeline, budget)
4. Sistem menyimpan proyek baru
5. Sistem menampilkan konfirmasi pembuatan berhasil
6. Admin dapat membuat kontrak untuk proyek (extend UC47)

### UC44: Update Project Status
**Aktor:** Admin  
**Deskripsi:** Mengupdate status dan progress proyek.  
**Prasyarat:** UC24  
**Alur Utama:**
1. Admin memilih proyek untuk diupdate
2. Admin mengubah status dan persentase progress
3. Admin mengisi catatan update
4. Sistem menyimpan perubahan
5. Sistem mengirim notifikasi ke customer

### UC45: Upload Project Files
**Aktor:** Admin  
**Deskripsi:** Mengupload file terkait proyek.  
**Prasyarat:** UC24  
**Alur Utama:**
1. Admin memilih proyek
2. Admin memilih opsi upload file
3. Admin memilih file dari perangkat
4. Sistem memvalidasi file
5. Sistem mengupload dan menyimpan file
6. Sistem menampilkan file di detail proyek

### UC46: Add Project Notes
**Aktor:** Admin  
**Deskripsi:** Menambahkan catatan pada proyek.  
**Prasyarat:** UC24  
**Alur Utama:**
1. Admin memilih proyek
2. Admin memilih opsi tambah catatan
3. Admin mengisi catatan
4. Sistem menyimpan catatan
5. Sistem menampilkan catatan di detail proyek

### UC47: Create Contract
**Aktor:** Admin  
**Deskripsi:** Membuat kontrak untuk proyek.  
**Prasyarat:** UC43  
**Alur Utama:**
1. Admin memilih proyek
2. Admin memilih opsi buat kontrak
3. Admin mengisi detail kontrak (nilai, syarat, jadwal pembayaran)
4. Sistem menghasilkan dokumen kontrak
5. Sistem menyimpan kontrak
6. Sistem mengirim notifikasi ke customer

## Communication System

### UC48: Send Message
**Aktor:** Customer, Admin  
**Deskripsi:** Mengirim pesan chat.  
**Prasyarat:** UC14 atau UC29  
**Alur Utama:**
1. Aktor mengisi pesan di form chat
2. Aktor mengirim pesan
3. Sistem menyimpan pesan
4. Sistem mengirimkan pesan ke penerima
5. Sistem menampilkan pesan di chat

### UC49: Receive Message
**Aktor:** Customer, Admin  
**Deskripsi:** Menerima pesan chat.  
**Prasyarat:** UC14 atau UC29  
**Alur Utama:**
1. Sistem menerima pesan baru
2. Sistem menampilkan notifikasi pesan baru
3. Sistem menampilkan pesan di chat

### UC50: View Message History
**Aktor:** Customer, Admin  
**Deskripsi:** Melihat history chat.  
**Prasyarat:** UC14 atau UC29  
**Alur Utama:**
1. Aktor mengakses halaman chat
2. Sistem menampilkan history chat
3. Aktor dapat melihat dan mencari pesan lama

### UC51: Send Notification
**Aktor:** System  
**Deskripsi:** Mengirim notifikasi ke pengguna.  
**Prasyarat:** -  
**Alur Utama:**
1. Sistem mendeteksi event yang memerlukan notifikasi
2. Sistem membuat notifikasi
3. Sistem mengirim notifikasi ke pengguna terkait
4. Sistem menampilkan badge notifikasi

### UC52: Mark as Read
**Aktor:** Customer, Admin
**Deskripsi:** Menandai notifikasi atau pesan sebagai telah dibaca.
**Prasyarat:** UC20 atau UC49
**Alur Utama:**
1. Aktor membuka notifikasi atau pesan
2. Sistem menandai sebagai telah dibaca
3. Sistem mengupdate badge notifikasi

## Include dan Extend Relationships

### Include Relationships
- **UC6 → UC7**: Registrasi otomatis melakukan login
- **UC15 → UC16**: Melihat kontrak termasuk kemampuan download PDF
- **UC23 → UC38**: Mengelola inquiry termasuk review detail
- **UC24 → UC44**: Mengelola proyek termasuk update status
- **UC24 → UC45**: Mengelola proyek termasuk upload file
- **UC24 → UC46**: Mengelola proyek termasuk menambah catatan
- **UC14 → UC48**: Chat customer termasuk mengirim pesan
- **UC14 → UC50**: Chat customer termasuk melihat history
- **UC29 → UC48**: Chat admin termasuk mengirim pesan
- **UC29 → UC50**: Chat admin termasuk melihat history
- **UC31 → UC34**: Membuat portfolio termasuk upload gambar
- **UC32 → UC34**: Edit portfolio termasuk upload gambar

### Extend Relationships
- **UC39 → UC41**: Menerima inquiry dapat diperluas dengan konversi ke proyek
- **UC41 → UC43**: Konversi ke proyek dapat diperluas dengan pembuatan proyek baru
- **UC43 → UC47**: Membuat proyek dapat diperluas dengan pembuatan kontrak
- **UC38 → UC39**: Review inquiry dapat diperluas dengan penerimaan
- **UC38 → UC40**: Review inquiry dapat diperluas dengan penolakan
- **UC11 → UC20**: Dashboard customer dapat diperluas dengan melihat notifikasi
- **UC21 → UC27**: Dashboard admin dapat diperluas dengan melihat analytics

## Mapping ke Controller Laravel

### Guest Features
- **HomeController**: UC1, UC2, UC3, UC8
- **ContactController**: UC4
- **InquiryController**: UC5
- **Auth\RegisterController**: UC6
- **Auth\LoginController**: UC7

### Customer Features
- **Customer\ProfileController**: UC10, UC17, UC18
- **Customer\DashboardController**: UC11, UC20
- **Customer\ProjectController**: UC12, UC19
- **Customer\InquiryController**: UC13
- **MessageController**: UC14, UC48, UC49, UC50, UC52
- **Customer\ContractController**: UC15, UC16

### Admin Features
- **Admin\DashboardController**: UC21, UC27
- **Admin\CustomerController**: UC22
- **Admin\InquiryController**: UC23, UC38, UC39, UC40, UC41
- **Admin\ProjectController**: UC24, UC43, UC44, UC45, UC46
- **Admin\ContractController**: UC25, UC47
- **Admin\ServiceController**: UC26
- **Admin\ReportsController**: UC28
- **Admin\MessageController**: UC29, UC48, UC49, UC50, UC52
- **Admin\PortfolioController**: UC31, UC32, UC33, UC34, UC35
- **NotificationController**: UC51
