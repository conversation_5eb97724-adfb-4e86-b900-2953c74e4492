# Activity Diagram - Customer Chat

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses menu chat]
    
    subgraph Customer ["👤 Customer"]
        A1[Akses menu chat]
        A2[Lihat riwayat chat]
        A3[Ketik pesan baru]
        A4[<PERSON><PERSON> pesan]
        A5[Lihat pesan terkirim]
        A6{<PERSON> balasan admin?}
        A7[Baca balasan admin]
        A8[Balas pesan opsional]
        A9[Selesai chat]
        A10[Menunggu balasan]
        A11[Selesai chat]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load halaman chat]
        S2[Tampilkan riwayat chat]
        S3[Cek status admin online]
        S4[Simpan pesan ke database]
        S5[<PERSON><PERSON> notifikasi ke admin]
        S6[Update tampilan chat]
        S7[Tampilkan pesan dari admin]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> A2
    A2 --> A3
    A3 --> A4
    A4 --> S4
    S4 --> S5
    S5 --> S6
    S6 --> A5
    A5 --> A6
    
    %% Decision branches
    A6 -->|Ya| S7
    S7 --> A7
    A7 --> A8
    A8 --> A9
    A9 --> End1([Stop])
    
    A6 -->|Tidak| A10
    A10 --> A11
    A11 --> End2([Stop])
    
    %% Styling
    classDef customerClass fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5,A7,A8,A9,A10,A11 customerClass
    class S1,S2,S3,S4,S5,S6,S7 sistemClass
    class A6 decisionClass
    class Start,End1,End2 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja customer saat menggunakan fitur chat dengan admin:

### Swimlane Customer:
- **Akses menu chat**: Customer mengklik menu chat
- **Lihat riwayat chat**: Customer melihat percakapan sebelumnya
- **Ketik pesan baru**: Customer mengetik pesan yang ingin dikirim
- **Kirim pesan**: Customer mengirim pesan ke admin
- **Lihat pesan terkirim**: Customer melihat pesan yang sudah terkirim
- **Baca balasan admin**: Customer membaca balasan dari admin (jika ada)
- **Balas pesan**: Customer dapat membalas pesan admin (opsional)
- **Menunggu balasan**: Customer menunggu balasan admin (jika belum ada)

### Swimlane Sistem:
- **Load halaman chat**: Sistem memuat halaman chat
- **Tampilkan riwayat chat**: Sistem menampilkan percakapan sebelumnya
- **Cek status admin online**: Sistem mengecek ketersediaan admin
- **Simpan pesan ke database**: Sistem menyimpan pesan customer
- **Kirim notifikasi ke admin**: Sistem memberitahu admin ada pesan baru
- **Update tampilan chat**: Sistem memperbarui tampilan chat
- **Tampilkan pesan dari admin**: Sistem menampilkan balasan admin

### Decision Points:
- **Ada balasan admin?**
  - **Ya**: Customer dapat membaca dan membalas pesan admin
  - **Tidak**: Customer menunggu balasan dari admin

### Karakteristik:
- **Actor**: Customer
- **Trigger**: Customer ingin berkomunikasi dengan admin
- **Precondition**: Customer sudah login ke sistem
- **Postcondition**: Pesan terkirim dan customer mendapat balasan atau menunggu

### Alur Proses:
1. Customer mengakses menu chat
2. Sistem menampilkan halaman chat dan riwayat percakapan
3. Customer mengetik dan mengirim pesan baru
4. Sistem menyimpan pesan dan mengirim notifikasi ke admin
5. Jika ada balasan: customer dapat membaca dan membalas
6. Jika belum ada balasan: customer menunggu balasan admin
