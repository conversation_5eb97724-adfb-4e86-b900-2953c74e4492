# Activity Diagram - Admin Dashboard

```mermaid
flowchart TD
    Start([Start]) --> A1[Login ke panel admin]
    
    subgraph Admin ["👤 Admin"]
        A1[Login ke panel admin]
        A2[Lihat dashboard]
        A3[Pilih menu yang diinginkan]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Validasi session admin]
        S2[Load dashboard admin]
        S3[Tampilkan statistik sistem]
        S4[Tampilkan menu navigasi]
        S5[Tampilkan notifikasi]
        S6{Menu yang dipilih?}
        S7[Redirect ke halaman portfolio]
        S8[Redirect ke halaman inquiry]
        S9[Redirect ke halaman proyek]
        S10[Redirect ke halaman kontrak]
        S11[Hapus session admin]
        S12[Redirect ke halaman login]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4
    S4 --> S5
    S5 --> A2
    A2 --> A3
    A3 --> S6
    
    %% Decision branches
    S6 -->|Kelola Portfolio| S7
    S6 -->|<PERSON><PERSON><PERSON> Permintaan| S8
    S6 -->|Kelola Proyek| S9
    S6 -->|Buat Kontrak| S10
    S6 -->|Logout| S11
    
    %% End points
    S7 --> End1([Stop])
    S8 --> End2([Stop])
    S9 --> End3([Stop])
    S10 --> End4([Stop])
    S11 --> S12
    S12 --> End5([Stop])
    
    %% Styling
    classDef adminClass fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3 adminClass
    class S1,S2,S3,S4,S5,S7,S8,S9,S10,S11,S12 sistemClass
    class S6 decisionClass
    class Start,End1,End2,End3,End4,End5 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja admin saat mengakses dan menggunakan dashboard admin:

### Swimlane Admin:
- **Login ke panel admin**: Admin melakukan proses login
- **Lihat dashboard**: Admin melihat tampilan dashboard
- **Pilih menu yang diinginkan**: Admin memilih menu navigasi

### Swimlane Sistem:
- **Validasi session admin**: Sistem memvalidasi kredensial admin
- **Load dashboard admin**: Sistem memuat halaman dashboard
- **Tampilkan statistik sistem**: Sistem menampilkan data statistik
- **Tampilkan menu navigasi**: Sistem menampilkan menu navigasi
- **Tampilkan notifikasi**: Sistem menampilkan notifikasi terbaru

### Decision Points:
Admin dapat memilih dari beberapa menu:
1. **Kelola Portfolio** → Redirect ke halaman portfolio
2. **Kelola Permintaan** → Redirect ke halaman inquiry  
3. **Kelola Proyek** → Redirect ke halaman proyek
4. **Buat Kontrak** → Redirect ke halaman kontrak
5. **Logout** → Hapus session dan redirect ke login

### Karakteristik:
- **Actor**: Admin
- **Trigger**: Login admin ke sistem
- **Precondition**: Admin memiliki kredensial yang valid
- **Postcondition**: Admin berhasil mengakses dashboard atau logout dari sistem
