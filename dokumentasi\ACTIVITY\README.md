# 📊 Activity Diagram - Sistem ARDFYA v2.1

Folder ini berisi seluruh activity diagram untuk sistem ARDFYA v2.1 yang menggambarkan alur aktivitas dan proses bisnis dalam aplikasi.

## 📁 Struktur File

### 🎯 **Activity Diagram Utama**
- `activity-main.puml` - Activity diagram utama sistem
- `activity-user-registration.puml` - Proses registrasi pengguna
- `activity-project-management.puml` - Proses manajemen proyek
- `activity-admin-workflow.puml` - Workflow admin

### 📋 **Activity Diagram Detail**
- `activity-login-process.puml` - Proses login pengguna
- `activity-inquiry-process.puml` - Proses pengajuan inquiry
- `activity-contract-creation.puml` - Proses pembuatan kontrak

## ⭐ Rekomendasi Penggunaan

### 🏆 **activity-main.puml** (UTAMA)
- **Deskripsi**: Activity diagram utama yang menggambarkan alur sistem secara keseluruhan
- **Cocok untuk**: Presentasi stakeholder, overview sistem

### 🎯 **activity-user-registration.puml** (REGISTRASI)
- **Deskripsi**: Detail proses registrasi dan login pengguna
- **Cocok untuk**: Dokumentasi teknis, development guide

### 📊 **activity-project-management.puml** (PROYEK)
- **Deskripsi**: Alur manajemen proyek dari inquiry hingga selesai
- **Cocok untuk**: Business process documentation

### ⚙️ **activity-admin-workflow.puml** (ADMIN)
- **Deskripsi**: Workflow admin dalam mengelola sistem
- **Cocok untuk**: Admin training, operational guide

## 🎨 Standar Desain

### ✅ **Desain Sederhana**
- **Mudah dibaca**: Font size yang lebih besar (12px)
- **Flow langsung**: Ketika gagal langsung ke endpoint, tidak menampilkan pesan error dulu
- **Swimlane model**: Menggunakan pembagian lane yang jelas per aktor
- **Minimal complexity**: Menghindari nested decision yang rumit

### ✅ **Flow yang Efisien**
- **Clean flow**: Flow tidak menimpa satu sama lain dengan spacing yang cukup
- **Sequential activities**: Setiap aktivitas memiliki urutan yang jelas
- **Proper swimlanes**: Pembagian lane yang rapi tanpa overlap
- **Clear transitions**: Perpindahan antar lane yang mudah diikuti
- **Straight lines**: Menggunakan flow lurus tanpa melengkung
- **Clear endpoints**: Start dan stop point yang tegas
- **Simple decisions**: Decision point yang sederhana dan mudah dipahami

### ✅ **Bahasa Indonesia**
- Semua label menggunakan bahasa Indonesia
- Terminologi yang konsisten dan singkat
- Mudah dipahami stakeholder lokal

## 🚀 Cara Penggunaan

1. **Buka file .puml** dengan PlantUML viewer
2. **Generate diagram** dalam format PNG/SVG
3. **Gunakan untuk dokumentasi** atau presentasi
4. **Update sesuai kebutuhan** pengembangan sistem

## 📝 Catatan Pengembangan

- Activity diagram dibuat berdasarkan use case yang telah didefinisikan
- Mengikuti alur bisnis yang sebenarnya dalam sistem ARDFYA
- Dapat diupdate seiring dengan pengembangan fitur baru
- Terintegrasi dengan dokumentasi UML lainnya

---
**Dibuat untuk**: Sistem ARDFYA v2.1  
**Tanggal**: 2025-06-21  
**Status**: Active Development
