@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Customer Lacak Proyek**

actor Customer
participant "Dashboard" as Dashboard
participant "Database" as DB

Customer -> Dashboard: Klik menu lacak proyek
activate Dashboard

Dashboard -> DB: Load daftar proyek customer
activate DB
DB --> Dashboard: Return daftar proyek
deactivate DB

Dashboard --> Customer: <PERSON><PERSON><PERSON><PERSON> daftar proyek

Customer -> Dashboard: <PERSON>lih proyek yang akan dilacak
Dashboard -> DB: Load detail proyek
activate DB
DB --> Dashboard: Return detail proyek dan timeline
deactivate DB

Dashboard -> DB: Load progress terbaru
activate DB
DB --> Dashboard: Return data progress
deactivate DB

Dashboard --> Customer: Tampilkan detail proyek
Dashboard --> Customer: Tampilkan timeline proyek
Dashboard --> Customer: Tampilkan progress terkini
Dashboard --> Customer: Tampilkan estimasi penyelesaian

Customer -> Customer: Lihat progress proyek
Customer -> Customer: Evaluasi timeline

deactivate Dashboard

@enduml
