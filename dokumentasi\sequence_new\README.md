# Sequence Diagram - ARDFYA System

## 📋 **Daftar Sequence Diagram**

Dokumentasi ini berisi sequence diagram untuk sistem ARDFYA yang dibuat berdasarkan activity diagram new. Setiap diagram menggunakan format PlantUML dengan styling yang konsisten dan mudah dibaca.

### **Guest Activities (Website Publik)**
1. **01-guest-lihat-beranda.puml** - Sequence diagram untuk guest melihat halaman beranda
2. **02-guest-lihat-portfolio.puml** - Sequence diagram untuk guest melihat portfolio

### **Customer Activities (Website Pelanggan)**
3. **03-customer-registrasi-login.puml** - Sequence diagram untuk login customer
4. **04-customer-dashboard.puml** - Sequence diagram untuk dashboard customer
5. **05-customer-lacak-proyek.puml** - Sequence diagram untuk customer melacak proyek
6. **06-customer-chat.puml** - Sequence diagram untuk fitur chat customer
7. **07-customer-ajukan-permintaan.puml** - Sequence diagram untuk customer mengajukan inquiry

### **Admin Activities (Panel Admin)**
8. **08-admin-login.puml** - Sequence diagram untuk login admin
9. **09-admin-dashboard.puml** - Sequence diagram untuk dashboard admin
10. **10-admin-kelola-portfolio.puml** - Sequence diagram untuk admin mengelola portfolio
11. **11-admin-kelola-permintaan.puml** - Sequence diagram untuk admin mengelola inquiry
12. **12-admin-kelola-proyek.puml** - Sequence diagram untuk admin mengelola proyek
13. **13-admin-buat-kontrak.puml** - Sequence diagram untuk admin membuat kontrak

## 🎯 **Karakteristik Diagram**

- **Format**: PlantUML Sequence Diagram
- **Styling**: Konsisten dengan tema biru dan orange
- **Participants**: Actor, System Components, Database, Notification
- **Konteks**: Berdasarkan activity diagram new
- **Kompleksitas**: Sederhana dan mudah dipahami
- **Hide Footbox**: Untuk tampilan yang lebih bersih

## 🎨 **Styling yang Diterapkan**

```plantuml
!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Participant styling
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

' Actor styling
skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

' Arrow styling
skinparam arrow {
    Color black
    Thickness 2
}
```

## 📝 **Cara Penggunaan**

1. Buka file `.puml` dengan PlantUML viewer
2. Atau copy kode ke PlantUML online editor
3. Generate diagram untuk melihat visualisasi

## 🔄 **Status**

✅ Semua 13 sequence diagram telah dibuat
✅ Format PlantUML telah diterapkan dengan benar
✅ Syntax PlantUML telah diperbaiki
✅ Styling konsisten dengan tema biru dan orange
✅ Hide footbox untuk tampilan yang bersih
✅ Dokumentasi lengkap dan siap digunakan

## 🛠️ **Update Terakhir**

### **Fitur yang Diterapkan:**
✅ **Hide Footbox** - Untuk tampilan yang lebih bersih tanpa footer
✅ **Consistent Styling** - Warna dan format yang seragam
✅ **Simple Flow** - Tidak menggunakan 'alt' blocks untuk kemudahan baca
✅ **Professional Layout** - Spacing dan alignment yang rapi
✅ **Note Integration** - Penjelasan tambahan menggunakan note

### **Participants yang Digunakan:**
- **Actor**: Guest, Customer, Admin
- **System**: Website ARDFYA, Dashboard, Admin Panel, Chat System
- **Backend**: Database, Auth System, PDF Generator
- **Services**: Notification, File Storage

### **Pola Interaksi:**
1. **User Action** → System
2. **System** → Database (load/save data)
3. **System** → External Services (notification, PDF)
4. **System** → User (response/feedback)

Semua sequence diagram telah disesuaikan dengan activity diagram new dan menggunakan pola interaksi yang konsisten untuk memudahkan pemahaman alur sistem.
