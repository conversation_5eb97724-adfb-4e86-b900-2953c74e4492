@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho
skinparam nodesep 60
skinparam ranksep 80
skinparam minlen 4
skinparam padding 15

title **Activity Diagram - Admin <PERSON>**

|Admin|
start
:Akses menu kelola permintaan;

|Sistem|
:Load daftar inquiry;
:Tampi<PERSON><PERSON> daftar permintaan;
:Tampilkan status inquiry;

|Admin|
:Lihat daftar inquiry;
:<PERSON><PERSON>h inquiry yang akan diproses;

|Sistem|
:<PERSON><PERSON><PERSON><PERSON> detail inquiry;
:<PERSON><PERSON><PERSON><PERSON> file pendukung;

|Admin|
:Baca detail permintaan;
:<PERSON>lih aksi yang diinginkan;

|Sistem|
if (Aksi yang dipilih?) then (Terima Inquiry)
  |Admin|
  :Ubah status menjadi diterima;
  :Input estimasi waktu dan biaya;

  |Sistem|
  :Update status inquiry;
  :Kirim notifikasi ke customer;
  stop
else if (Tolak Inquiry)
  |Admin|
  :Ubah status menjadi ditolak;
  :Input alasan penolakan;

  |Sistem|
  :Update status inquiry;
  :Kirim notifikasi ke customer;
  stop
else (Proses ke Proyek)
  |Admin|
  :Konversi inquiry menjadi proyek;

  |Sistem|
  :Buat data proyek baru;
  :Update status inquiry;
  :Kirim notifikasi ke customer;
  stop
endif

@enduml
