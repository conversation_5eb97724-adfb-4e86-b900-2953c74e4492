@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Admin Dashboard**

actor Admin
participant "Admin Panel" as Panel
participant "Database" as DB

Admin -> Panel: Aks<PERSON> dashboard admin
activate Panel

Panel -> DB: Load statistik sistem
activate DB
DB --> Panel: Return jumlah customer, proyek, permintaan
deactivate DB

Panel -> DB: Load permintaan terbaru
activate DB
DB --> Panel: Return daftar permintaan pending
deactivate DB

Panel -> DB: Load proyek aktif
activate DB
DB --> Panel: Return daftar proyek dalam progress
deactivate DB

Panel --> Admin: <PERSON><PERSON><PERSON>an dashboard admin
Panel --> Admin: Tampilkan statistik sistem
Panel --> Admin: Tampilkan permintaan pending
Panel --> Admin: Tampilkan proyek aktif
Panel --> Admin: Tampilkan menu navigasi admin

Admin -> Admin: Lihat ringkasan sistem
Admin -> Admin: Evaluasi workload

deactivate Panel

@enduml
