@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Customer Dashboard**

actor Customer
participant "Dashboard" as Dashboard
participant "Database" as DB

Customer -> Dashboard: Akses dashboard customer
activate Dashboard

Dashboard -> DB: Load data customer
activate DB
DB --> Dashboard: Return profile customer
deactivate DB

Dashboard -> DB: Load data proyek customer
activate DB
DB --> Dashboard: Return daftar proyek
deactivate DB

Dashboard -> DB: Load notifikasi terbaru
activate DB
DB --> Dashboard: Return notifikasi
deactivate DB

Dashboard --> Customer: <PERSON><PERSON><PERSON><PERSON> dashboard
Dashboard --> Customer: <PERSON><PERSON><PERSON><PERSON> ring<PERSON>an proyek
Dashboard --> Customer: Tampilkan status proyek aktif
Dashboard --> Customer: Tampilkan notifikasi

Customer -> Customer: Lihat ringkasan dashboard
Customer -> Customer: Cek status proyek

deactivate Dashboard

@enduml
