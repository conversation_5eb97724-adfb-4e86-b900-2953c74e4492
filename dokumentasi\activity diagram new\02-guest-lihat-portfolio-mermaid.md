# Activity Diagram - Guest <PERSON><PERSON> Portfolio

```mermaid
flowchart TD
    Start([Start]) --> A1[<PERSON>kses halaman portfolio]
    
    subgraph Guest ["👤 Guest"]
        A1[Akses halaman portfolio]
        A2[Pilih kategori portfolio]
        A3[Lihat daftar portfolio]
        A4[Klik detail portfolio]
        A5[Lihat detail portfolio]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load data portfolio]
        S2[Filter berdasarkan kategori]
        S3[Tampilkan daftar portfolio]
        S4[Tampilkan detail portfolio]
        S5[Tampilkan gambar dan deskripsi]
    end
    
    %% Flow connections
    A1 --> A2
    A2 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> A3
    A3 --> A4
    A4 --> S4
    S4 --> S5
    S5 --> A5
    A5 --> End([Stop])
    
    %% Styling
    classDef guestClass fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5 guestClass
    class S1,S2,S3,S4,S5 sistemClass
    class Start,End startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja guest saat mengakses dan melihat portfolio proyek ARDFYA:

### Swimlane Guest:
- **Akses halaman portfolio**: Guest mengunjungi halaman portfolio
- **Pilih kategori portfolio**: Guest memilih kategori yang diinginkan
- **Lihat daftar portfolio**: Guest melihat daftar portfolio yang tersedia
- **Klik detail portfolio**: Guest mengklik portfolio untuk melihat detail
- **Lihat detail portfolio**: Guest melihat informasi lengkap portfolio

### Swimlane Sistem:
- **Load data portfolio**: Sistem memuat data portfolio dari database
- **Filter berdasarkan kategori**: Sistem memfilter portfolio sesuai kategori
- **Tampilkan daftar portfolio**: Sistem menampilkan daftar portfolio
- **Tampilkan detail portfolio**: Sistem menampilkan halaman detail
- **Tampilkan gambar dan deskripsi**: Sistem menampilkan konten lengkap

### Karakteristik:
- **Actor**: Guest (pengunjung website)
- **Trigger**: Guest mengakses halaman portfolio
- **Precondition**: Data portfolio tersedia di sistem
- **Postcondition**: Guest berhasil melihat portfolio dan detailnya

### Alur Proses:
1. Guest mengakses halaman portfolio
2. Guest memilih kategori portfolio yang diinginkan
3. Sistem memuat dan memfilter data portfolio
4. Guest melihat daftar portfolio dan mengklik detail
5. Sistem menampilkan informasi lengkap portfolio yang dipilih
