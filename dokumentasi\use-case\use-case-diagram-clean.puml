@startuml Use Case Diagram - ARDFYA

!theme plain
skinparam handwritten false
skinparam shadowing false

' Styling
skinparam usecase {
    BackgroundColor #E1F5FE
    BorderColor #0288D1
    FontSize 11
    BorderThickness 2
}

skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #01579B
    FontSize 12
    BorderThickness 2
}

skinparam rectangle {
    BackgroundColor #FAFAFA
    BorderColor #424242
    FontSize 14
    BorderThickness 3
}

title **Use Case Diagram - ARDFYA Construction Management System**

' Actors positioned outside system
actor "Guest" as G
actor "Customer" as C
actor "Admin" as A

' System boundary dengan semua use case dalam grid layout
rectangle "**ARDFYA System**" {
  
  ' Row 1 - Public Features
  usecase "Browse Homepage" as UC1
  usecase "View Portfolio" as UC2
  usecase "Submit Inquiry" as UC3
  usecase "Register Account" as UC4
  
  ' Row 2 - Authentication & Customer Dashboard
  usecase "Login" as UC5
  usecase "View Dashboard" as UC6
  usecase "Track Projects" as UC7
  usecase "Chat with Admin" as UC8
  
  ' Row 3 - Customer Features
  usecase "View Contracts" as UC9
  usecase "Download Contract" as UC10
  usecase "Manage Profile" as UC11
  usecase "Manage Customers" as UC12
  
  ' Row 4 - Admin Management
  usecase "Manage Inquiries" as UC13
  usecase "Manage Projects" as UC14
  usecase "Manage Portfolio" as UC15
  usecase "Create Contracts" as UC16
  
  ' Row 5 - Communication
  usecase "Chat with Customer" as UC17
  
  ' Layout positioning untuk grid yang rapi
  UC1 -[hidden]- UC2
  UC2 -[hidden]- UC3
  UC3 -[hidden]- UC4
  
  UC5 -[hidden]- UC6
  UC6 -[hidden]- UC7
  UC7 -[hidden]- UC8
  
  UC9 -[hidden]- UC10
  UC10 -[hidden]- UC11
  UC11 -[hidden]- UC12
  
  UC13 -[hidden]- UC14
  UC14 -[hidden]- UC15
  UC15 -[hidden]- UC16
  
  UC1 -[hidden]d- UC5
  UC2 -[hidden]d- UC6
  UC3 -[hidden]d- UC7
  UC4 -[hidden]d- UC8
  
  UC5 -[hidden]d- UC9
  UC6 -[hidden]d- UC10
  UC7 -[hidden]d- UC11
  UC8 -[hidden]d- UC12
  
  UC9 -[hidden]d- UC13
  UC10 -[hidden]d- UC14
  UC11 -[hidden]d- UC15
  UC12 -[hidden]d- UC16
  
  UC13 -[hidden]d- UC17
}

' Guest relationships
G --> UC1 : accesses
G --> UC2 : views
G --> UC3 : submits
G --> UC4 : registers
G --> UC5 : logs in

' Customer relationships (Customer juga bisa login dan akses public features)
C --> UC1 : accesses
C --> UC2 : views
C --> UC3 : submits
C --> UC5 : logs in
C --> UC6 : views
C --> UC7 : tracks
C --> UC8 : communicates
C --> UC9 : views
C --> UC11 : manages

' Admin relationships (Admin juga bisa login dan akses semua features)
A --> UC1 : accesses
A --> UC2 : views
A --> UC5 : logs in
A --> UC12 : manages
A --> UC13 : manages
A --> UC14 : manages
A --> UC15 : manages
A --> UC16 : creates
A --> UC17 : communicates

' Include relationships
UC4 ..> UC5 : <<include>>
UC9 ..> UC10 : <<include>>
UC8 ..> UC17 : <<include>>

' Extend relationships
UC13 ..> UC14 : <<extend>>
UC14 ..> UC16 : <<extend>>

@enduml
