# Use Case Diagram - ARDFYA v2.1 (Mermaid Version)

```mermaid
graph TB
    %% ===== ACTORS =====
    G[🌐 Guest]
    C[👤 Customer] 
    A[⚙️ Admin]
    
    %% ===== SYSTEM BOUNDARY =====
    subgraph MainSystem["🏢 ARDFYA v2.1 System"]
        
        %% ===== PUBLIC FEATURES =====
        subgraph PublicPkg["🌐 Public Features"]
            UC01[🏠 Browse Homepage]
            UC02[🖼️ View Portfolio Gallery]
            UC03[📋 View Portfolio Detail]
            UC04[📧 Submit Contact Form]
            UC05[📝 Submit Inquiry]
            UC06[👤 Register Account]
            UC07[🔐 Login to System]
            UC08[ℹ️ View About Page]
        end
        
        %% ===== CUSTOMER FEATURES =====
        subgraph CustomerPkg["👤 Customer Features"]
            UC10[⚙️ Manage Profile]
            UC11[📊 View Dashboard]
            UC12[📈 Track My Projects]
            UC13[📋 View My Inquiries]
            UC14[💬 Chat with Admin]
            UC15[📄 View My Contracts]
            UC16[⬇️ Download Contract PDF]
            UC17[✏️ Update Profile Info]
            UC18[🔑 Change Password]
            UC19[📊 View Project Progress]
            UC20[🔔 View Notifications]
        end
        
        %% ===== ADMIN MANAGEMENT =====
        subgraph AdminPkg["⚙️ Admin Management"]
            UC21[🏠 Access Admin Dashboard]
            UC22[👥 Manage Customers]
            UC23[📋 Manage Inquiries]
            UC24[🏗️ Manage Projects]
            UC25[📄 Manage Contracts]
            UC26[🛠️ Manage Services]
            UC27[📊 View Analytics]
            UC28[📈 Generate Reports]
            UC29[💬 Admin Chat Management]
        end
        
        %% ===== PORTFOLIO MANAGEMENT =====
        subgraph PortfolioPkg["🎨 Portfolio Management"]
            UC31[➕ Create Portfolio]
            UC32[✏️ Edit Portfolio]
            UC33[🗑️ Delete Portfolio]
            UC34[📷 Upload Portfolio Images]
            UC35[⭐ Set Featured Portfolio]
        end
        
        %% ===== INQUIRY MANAGEMENT =====
        subgraph InquiryPkg["📝 Inquiry Management"]
            UC38[👁️ Review Inquiry]
            UC39[✅ Accept Inquiry]
            UC40[❌ Reject Inquiry]
            UC41[🔄 Convert to Project]
        end
        
        %% ===== PROJECT MANAGEMENT =====
        subgraph ProjectPkg["🏗️ Project Management"]
            UC43[➕ Create Project]
            UC44[🔄 Update Project Status]
            UC45[📤 Upload Project Files]
            UC46[📝 Add Project Notes]
            UC47[📄 Create Contract]
        end
        
        %% ===== COMMUNICATION SYSTEM =====
        subgraph CommPkg["💬 Communication System"]
            UC48[📤 Send Message]
            UC49[📥 Receive Message]
            UC50[📋 View Message History]
            UC51[🔔 Send Notification]
            UC52[✓ Mark as Read]
        end
    end
    
    %% ===== GUEST RELATIONSHIPS =====
    G --> UC01
    G --> UC02
    G --> UC03
    G --> UC04
    G --> UC05
    G --> UC06
    G --> UC07
    G --> UC08
    
    %% ===== CUSTOMER RELATIONSHIPS =====
    C --> UC10
    C --> UC11
    C --> UC12
    C --> UC13
    C --> UC14
    C --> UC15
    C --> UC17
    C --> UC18
    C --> UC19
    C --> UC20
    C --> UC05
    
    %% ===== ADMIN RELATIONSHIPS =====
    A --> UC21
    A --> UC22
    A --> UC23
    A --> UC24
    A --> UC25
    A --> UC26
    A --> UC27
    A --> UC28
    A --> UC29
    A --> UC31
    A --> UC32
    A --> UC33
    A --> UC34
    A --> UC35
    A --> UC38
    A --> UC39
    A --> UC40
    A --> UC41
    A --> UC43
    A --> UC44
    A --> UC45
    A --> UC46
    A --> UC47
    A --> UC48
    
    %% ===== INCLUDE RELATIONSHIPS =====
    UC06 -.->|include| UC07
    UC15 -.->|include| UC16
    UC23 -.->|include| UC38
    UC24 -.->|include| UC44
    UC24 -.->|include| UC45
    UC24 -.->|include| UC46
    UC14 -.->|include| UC48
    UC14 -.->|include| UC50
    UC29 -.->|include| UC48
    UC29 -.->|include| UC50
    UC31 -.->|include| UC34
    UC32 -.->|include| UC34
    
    %% ===== EXTEND RELATIONSHIPS =====
    UC39 -.->|extend| UC41
    UC41 -.->|extend| UC43
    UC43 -.->|extend| UC47
    UC38 -.->|extend| UC39
    UC38 -.->|extend| UC40
    UC11 -.->|extend| UC20
    UC21 -.->|extend| UC27
    
    %% ===== STYLING =====
    classDef guestClass fill:#E3F2FD,stroke:#01579B,stroke-width:3px,color:#000
    classDef customerClass fill:#E1F5FE,stroke:#0288D1,stroke-width:3px,color:#000
    classDef adminClass fill:#E8F5E9,stroke:#2E7D32,stroke-width:3px,color:#000
    classDef publicClass fill:#F3E5F5,stroke:#7B1FA2,stroke-width:2px,color:#000
    classDef systemClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    
    class G guestClass
    class C customerClass
    class A adminClass
    class UC01,UC02,UC03,UC04,UC05,UC06,UC07,UC08 publicClass
    class UC10,UC11,UC12,UC13,UC14,UC15,UC16,UC17,UC18,UC19,UC20 customerClass
    class UC21,UC22,UC23,UC24,UC25,UC26,UC27,UC28,UC29,UC31,UC32,UC33,UC34,UC35,UC38,UC39,UC40,UC41,UC43,UC44,UC45,UC46,UC47,UC48,UC49,UC50,UC51,UC52 adminClass
```

## Penjelasan Diagram

### 🎯 **Actors**
- **🌐 Guest**: Pengunjung website yang belum login
- **👤 Customer**: Pelanggan terdaftar dengan akses ke fitur pribadi
- **⚙️ Admin**: Administrator dengan akses penuh ke sistem

### 📦 **Feature Packages**

#### 🌐 **Public Features** (8 Use Cases)
Fitur yang dapat diakses oleh Guest tanpa login:
- Browse Homepage, View Portfolio, Submit Forms, Register, Login

#### 👤 **Customer Features** (11 Use Cases)  
Fitur khusus untuk Customer yang sudah login:
- Dashboard, Project Tracking, Chat, Contract Management, Profile

#### ⚙️ **Admin Management** (9 Use Cases)
Fitur manajemen utama untuk Admin:
- Dashboard, Customer Management, Analytics, Reports

#### 🎨 **Portfolio Management** (5 Use Cases)
Manajemen portfolio proyek:
- CRUD Portfolio, Image Upload, Featured Setting

#### 📝 **Inquiry Management** (4 Use Cases)
Manajemen permintaan layanan:
- Review, Accept/Reject, Convert to Project

#### 🏗️ **Project Management** (5 Use Cases)
Manajemen proyek konstruksi:
- Create, Update Status, File Upload, Notes, Contract Creation

#### 💬 **Communication System** (5 Use Cases)
Sistem komunikasi real-time:
- Messaging, Notifications, Read Status

### 🔗 **Relationships**

#### **Include Relationships** (Mandatory)
- UC06 → UC07: Register includes Login
- UC15 → UC16: View Contracts includes Download
- UC23 → UC38: Manage Inquiries includes Review
- UC24 → UC44/45/46: Manage Projects includes Status/Files/Notes
- UC14/29 → UC48/50: Chat includes Send/History
- UC31/32 → UC34: Portfolio includes Image Upload

#### **Extend Relationships** (Optional)
- UC38 → UC39/40: Review extends to Accept/Reject
- UC39 → UC41: Accept extends to Convert
- UC41 → UC43: Convert extends to Create Project
- UC43 → UC47: Create Project extends to Create Contract
- UC11 → UC20: Dashboard extends to Notifications
- UC21 → UC27: Admin Dashboard extends to Analytics

### 📊 **Statistics**
- **Total Use Cases**: 47
- **Include Relationships**: 12
- **Extend Relationships**: 7
- **Feature Packages**: 7
- **Actors**: 3

### 🎨 **Visual Features**
- **Emoji Icons**: Setiap use case memiliki emoji untuk identifikasi visual
- **Color Coding**: Berbeda warna untuk setiap actor dan package
- **Clear Grouping**: Package terorganisir berdasarkan functionality
- **Relationship Labels**: Include/extend relationships dengan label yang jelas
