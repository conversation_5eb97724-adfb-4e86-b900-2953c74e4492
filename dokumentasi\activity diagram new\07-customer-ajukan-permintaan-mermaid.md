# Activity Diagram - Customer Ajukan Permintaan

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses menu ajukan permintaan]
    
    subgraph Customer ["👤 Customer"]
        A1[Akses menu ajukan permintaan]
        A2[Isi form permintaan]
        A3[Input judul permintaan]
        A4[Input deskripsi kebutuhan]
        A5[Upload file pendukung opsional]
        A6[Submit form]
        A7[Terima konfirmasi pengajuan]
        A8[Catat nomor tiket]
        A9[Perbaiki form]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load form inquiry]
        S2[Tampilkan form permintaan]
        S3[Validasi data form]
        S4{Data valid?}
        S5[Simpan inquiry ke database]
        S6[Generate nomor tiket]
        S7[Kirim notifikasi ke admin]
        S8[Kirim email konfirmasi ke customer]
        S9[Tampilkan pesan error]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> A2
    A2 --> A3
    A3 --> A4
    A4 --> A5
    A5 --> A6
    A6 --> S3
    S3 --> S4
    
    %% Decision branches
    S4 -->|Ya| S5
    S5 --> S6
    S6 --> S7
    S7 --> S8
    S8 --> A7
    A7 --> A8
    A8 --> End1([Stop])
    
    S4 -->|Tidak| S9
    S9 --> A9
    A9 --> End2([Stop])
    
    %% Styling
    classDef customerClass fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5,A6,A7,A8,A9 customerClass
    class S1,S2,S3,S5,S6,S7,S8,S9 sistemClass
    class S4 decisionClass
    class Start,End1,End2 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja customer saat mengajukan permintaan layanan baru:

### Swimlane Customer:
- **Akses menu ajukan permintaan**: Customer mengklik menu untuk membuat permintaan
- **Isi form permintaan**: Customer mengisi formulir permintaan
- **Input judul permintaan**: Customer memasukkan judul permintaan
- **Input deskripsi kebutuhan**: Customer menjelaskan kebutuhan detail
- **Upload file pendukung**: Customer mengunggah file pendukung (opsional)
- **Submit form**: Customer mengirim formulir permintaan
- **Terima konfirmasi pengajuan**: Customer menerima konfirmasi (jika valid)
- **Catat nomor tiket**: Customer mencatat nomor tiket untuk tracking
- **Perbaiki form**: Customer memperbaiki form (jika tidak valid)

### Swimlane Sistem:
- **Load form inquiry**: Sistem memuat formulir permintaan
- **Tampilkan form permintaan**: Sistem menampilkan form ke customer
- **Validasi data form**: Sistem memvalidasi data yang diinput
- **Simpan inquiry ke database**: Sistem menyimpan permintaan ke database
- **Generate nomor tiket**: Sistem membuat nomor tiket unik
- **Kirim notifikasi ke admin**: Sistem memberitahu admin ada permintaan baru
- **Kirim email konfirmasi**: Sistem mengirim email konfirmasi ke customer
- **Tampilkan pesan error**: Sistem menampilkan pesan kesalahan

### Decision Points:
- **Data valid?**
  - **Ya**: Simpan inquiry → Generate tiket → Kirim notifikasi → Konfirmasi
  - **Tidak**: Tampilkan error → Customer perbaiki form

### Karakteristik:
- **Actor**: Customer
- **Trigger**: Customer ingin mengajukan permintaan layanan
- **Precondition**: Customer sudah login ke sistem
- **Postcondition**: Permintaan tersimpan dan admin mendapat notifikasi, atau customer memperbaiki form

### Alur Proses:
1. Customer mengakses menu ajukan permintaan
2. Sistem menampilkan formulir permintaan
3. Customer mengisi form (judul, deskripsi, file pendukung)
4. Customer submit form dan sistem validasi
5. Jika valid: sistem simpan, generate tiket, kirim notifikasi dan konfirmasi
6. Jika tidak valid: sistem tampilkan error dan customer perbaiki form
