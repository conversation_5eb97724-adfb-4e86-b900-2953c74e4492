# Activity Diagram - Customer Lacak Proyek

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses menu lacak proyek]
    
    subgraph Customer ["👤 Customer"]
        A1[Akses menu lacak proyek]
        A2[Lihat daftar proyek]
        A3[<PERSON><PERSON>h proyek yang ingin dilacak]
        A4[Lihat detail progress]
        A5{Ada kontrak?}
        A6[Download kontrak PDF]
        A7[Lihat status proyek saja]
        A8[Selesai melihat proyek]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load data proyek customer]
        S2[Tampilkan daftar proyek]
        S3[Tampilkan status proyek]
        S4[Load detail proyek]
        S5[Tampilkan timeline proyek]
        S6[Tampilkan progress proyek]
        S7[Tampilkan file kontrak jika ada]
        S8[Generate file kontrak]
        S9[Download kontrak]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> A2
    A2 --> A3
    A3 --> S4
    S4 --> S5
    S5 --> S6
    S6 --> S7
    S7 --> A4
    A4 --> A5
    
    %% Decision branches
    A5 -->|Ya| A6
    A6 --> S8
    S8 --> S9
    S9 --> End1([Stop])
    
    A5 -->|Tidak| A7
    A7 --> A8
    A8 --> End2([Stop])
    
    %% Styling
    classDef customerClass fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A6,A7,A8 customerClass
    class S1,S2,S3,S4,S5,S6,S7,S8,S9 sistemClass
    class A5 decisionClass
    class Start,End1,End2 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja customer saat melacak progress proyek mereka:

### Swimlane Customer:
- **Akses menu lacak proyek**: Customer mengklik menu lacak proyek
- **Lihat daftar proyek**: Customer melihat semua proyek mereka
- **Pilih proyek yang ingin dilacak**: Customer memilih proyek spesifik
- **Lihat detail progress**: Customer melihat detail kemajuan proyek
- **Download kontrak PDF**: Customer mengunduh kontrak (jika tersedia)
- **Lihat status proyek saja**: Customer hanya melihat status (jika tidak ada kontrak)

### Swimlane Sistem:
- **Load data proyek customer**: Sistem memuat data proyek milik customer
- **Tampilkan daftar proyek**: Sistem menampilkan daftar proyek
- **Tampilkan status proyek**: Sistem menampilkan status setiap proyek
- **Load detail proyek**: Sistem memuat detail proyek yang dipilih
- **Tampilkan timeline proyek**: Sistem menampilkan timeline kemajuan
- **Tampilkan progress proyek**: Sistem menampilkan persentase progress
- **Tampilkan file kontrak**: Sistem menampilkan kontrak jika tersedia
- **Generate file kontrak**: Sistem membuat file PDF kontrak
- **Download kontrak**: Sistem mengirim file untuk diunduh

### Decision Points:
- **Ada kontrak?**
  - **Ya**: Customer dapat download kontrak PDF
  - **Tidak**: Customer hanya melihat status proyek

### Karakteristik:
- **Actor**: Customer
- **Trigger**: Customer ingin melacak progress proyek
- **Precondition**: Customer memiliki proyek yang sedang berjalan
- **Postcondition**: Customer mendapat informasi progress atau mengunduh kontrak

### Alur Proses:
1. Customer mengakses menu lacak proyek
2. Sistem menampilkan daftar proyek customer
3. Customer memilih proyek yang ingin dilacak
4. Sistem menampilkan detail progress dan timeline
5. Jika ada kontrak: customer dapat mengunduh PDF
6. Jika tidak ada kontrak: customer hanya melihat status
