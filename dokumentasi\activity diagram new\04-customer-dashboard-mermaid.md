# Activity Diagram - Customer Dashboard

```mermaid
flowchart TD
    Start([Start]) --> A1[Login ke sistem]
    
    subgraph Customer ["👤 Customer"]
        A1[Login ke sistem]
        A2[Lihat dashboard]
        A3[Pilih menu yang diinginkan]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Validasi session customer]
        S2[Load dashboard customer]
        S3[Tampilkan menu navigasi]
        S4[Tampilkan ringkasan proyek]
        S5[Tampilkan status inquiry]
        S6{Menu yang dipilih?}
        S7[Redirect ke halaman proyek]
        S8[Redirect ke halaman chat]
        S9[Redirect ke halaman inquiry]
        S10[Hapus session customer]
        S11[Redirect ke halaman login]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4
    S4 --> S5
    S5 --> A2
    A2 --> A3
    A3 --> S6
    
    %% Decision branches
    S6 -->|Lacak Proyek| S7
    S6 -->|Chat| S8
    S6 -->|Ajukan Permintaan| S9
    S6 -->|Logout| S10
    
    %% End points
    S7 --> End1([Stop])
    S8 --> End2([Stop])
    S9 --> End3([Stop])
    S10 --> S11
    S11 --> End4([Stop])
    
    %% Styling
    classDef customerClass fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3 customerClass
    class S1,S2,S3,S4,S5,S7,S8,S9,S10,S11 sistemClass
    class S6 decisionClass
    class Start,End1,End2,End3,End4 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja customer saat mengakses dan menggunakan dashboard customer:

### Swimlane Customer:
- **Login ke sistem**: Customer melakukan proses login
- **Lihat dashboard**: Customer melihat tampilan dashboard
- **Pilih menu yang diinginkan**: Customer memilih menu navigasi

### Swimlane Sistem:
- **Validasi session customer**: Sistem memvalidasi session customer
- **Load dashboard customer**: Sistem memuat halaman dashboard
- **Tampilkan menu navigasi**: Sistem menampilkan menu navigasi
- **Tampilkan ringkasan proyek**: Sistem menampilkan ringkasan proyek customer
- **Tampilkan status inquiry**: Sistem menampilkan status permintaan

### Decision Points:
Customer dapat memilih dari beberapa menu:
1. **Lacak Proyek** → Redirect ke halaman proyek
2. **Chat** → Redirect ke halaman chat
3. **Ajukan Permintaan** → Redirect ke halaman inquiry
4. **Logout** → Hapus session dan redirect ke login

### Karakteristik:
- **Actor**: Customer
- **Trigger**: Customer login ke sistem
- **Precondition**: Customer memiliki session yang valid
- **Postcondition**: Customer berhasil mengakses dashboard atau logout dari sistem

### Alur Proses:
1. Customer login ke sistem
2. Sistem validasi session dan load dashboard
3. Sistem menampilkan informasi dashboard (menu, ringkasan, status)
4. Customer melihat dashboard dan memilih menu
5. Sistem mengarahkan ke halaman yang dipilih atau logout
