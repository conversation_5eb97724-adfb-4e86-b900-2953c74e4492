# Activity Diagram - Admin Kelola Proyek

```mermaid
flowchart TD
    Start([Start]) --> A1[Akses menu kelola proyek]
    
    subgraph Admin ["👤 Admin"]
        A1[Akses menu kelola proyek]
        A2[Lihat daftar proyek]
        A3[<PERSON>lih proyek yang akan dikelola]
        A4[<PERSON>lih menu aksi]
        A5[Input data perubahan]
        A6[Konfirmasi aksi]
        A7[<PERSON><PERSON> hasil perubahan]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Load daftar proyek]
        S2[Tampilkan daftar proyek]
        S3[Tampilkan status proyek]
        S4[Tampilkan detail proyek]
        S5[Tampilkan timeline proyek]
        S6[Proses request aksi]
        S7[Tampilkan form sesuai aksi]
        S8[Validasi dan simpan data]
        S9[Update status proyek]
        S10[Kirim notifikasi ke customer]
        S11[Update tampilan daftar proyek]
    end
    
    %% Flow connections
    A1 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> A2
    A2 --> A3
    A3 --> S4
    S4 --> S5
    S5 --> A4
    A4 --> S6
    S6 --> S7
    S7 --> A5
    A5 --> A6
    A6 --> S8
    S8 --> S9
    S9 --> S10
    S10 --> S11
    S11 --> A7
    A7 --> End([Stop])
    
    %% Note for menu actions
    A4 -.-> Note1["`**Pilihan Menu Aksi:**
    • Update Progress
    • Edit Detail Proyek  
    • Selesaikan Proyek`"]
    
    %% Styling
    classDef adminClass fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    classDef noteClass fill:#FFF9C4,stroke:#F57F17,stroke-width:1px,color:#000
    
    class A1,A2,A3,A4,A5,A6,A7 adminClass
    class S1,S2,S3,S4,S5,S6,S7,S8,S9,S10,S11 sistemClass
    class Start,End startEndClass
    class Note1 noteClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja admin saat mengelola proyek yang sedang berjalan:

### Swimlane Admin:
- **Akses menu kelola proyek**: Admin mengklik menu kelola proyek
- **Lihat daftar proyek**: Admin melihat semua proyek yang ada
- **Pilih proyek yang akan dikelola**: Admin memilih proyek spesifik
- **Pilih menu aksi**: Admin memilih jenis aksi yang akan dilakukan
- **Input data perubahan**: Admin mengisi form perubahan
- **Konfirmasi aksi**: Admin mengkonfirmasi perubahan
- **Lihat hasil perubahan**: Admin melihat hasil update

### Swimlane Sistem:
- **Load daftar proyek**: Sistem memuat semua proyek dari database
- **Tampilkan daftar proyek**: Sistem menampilkan daftar proyek dengan status
- **Tampilkan status proyek**: Sistem menampilkan status setiap proyek
- **Tampilkan detail proyek**: Sistem menampilkan detail proyek yang dipilih
- **Tampilkan timeline proyek**: Sistem menampilkan timeline kemajuan
- **Proses request aksi**: Sistem memproses permintaan aksi admin
- **Tampilkan form sesuai aksi**: Sistem menampilkan form yang sesuai
- **Validasi dan simpan data**: Sistem memvalidasi dan menyimpan perubahan
- **Update status proyek**: Sistem mengupdate status proyek
- **Kirim notifikasi ke customer**: Sistem mengirim notifikasi ke customer
- **Update tampilan daftar proyek**: Sistem memperbarui tampilan

### Menu Aksi yang Tersedia:

1. **Update Progress**:
   - Input persentase kemajuan proyek
   - Update milestone yang telah dicapai
   - Tambah catatan progress

2. **Edit Detail Proyek**:
   - Edit informasi proyek (judul, deskripsi)
   - Update estimasi waktu dan biaya
   - Ubah spesifikasi proyek

3. **Selesaikan Proyek**:
   - Ubah status menjadi "selesai"
   - Input catatan penyelesaian
   - Generate laporan akhir

### Karakteristik:
- **Actor**: Admin
- **Trigger**: Admin ingin mengelola progress proyek
- **Precondition**: Ada proyek yang perlu dikelola
- **Postcondition**: Proyek terupdate dan customer mendapat notifikasi

### Alur Proses:
1. Admin mengakses menu kelola proyek
2. Sistem menampilkan daftar proyek dengan status dan timeline
3. Admin memilih proyek dan jenis aksi yang akan dilakukan
4. Sistem menampilkan form sesuai dengan aksi yang dipilih
5. Admin mengisi data perubahan dan konfirmasi
6. Sistem validasi, simpan, update status, dan kirim notifikasi
7. Admin melihat hasil perubahan pada daftar proyek
