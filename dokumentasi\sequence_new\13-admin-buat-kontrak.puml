@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Admin Buat Kontrak**

actor Admin
participant "Admin Panel" as Panel
participant "Database" as DB
participant "PDF Generator" as PDF
participant "Notification" as Notif

Admin -> Panel: Akses menu buat kontrak
activate Panel

Panel -> DB: Load daftar permintaan yang diterima
activate DB
DB --> Panel: Return permintaan yang siap dikontrak
deactivate DB

Panel --> Admin: <PERSON><PERSON>lkan daftar permintaan

Admin -> Panel: <PERSON><PERSON><PERSON> permintaan untuk dikontrak
Panel -> DB: Load detail permintaan dan customer
activate DB
DB --> Panel: Return data lengkap permintaan
deactivate DB

Panel --> Admin: Tampilkan form buat kontrak

Admin -> Panel: Input detail kontrak
Admin -> Panel: Input harga dan timeline
Admin -> Panel: Input syarat dan ketentuan
Admin -> Panel: Klik tombol generate kontrak

Panel -> PDF: Generate dokumen kontrak PDF
activate PDF
PDF --> Panel: Return file PDF kontrak
deactivate PDF

Panel -> DB: Simpan data kontrak dan file PDF
activate DB
DB --> Panel: Return konfirmasi kontrak tersimpan
deactivate DB

Panel -> Notif: Kirim notifikasi kontrak ke customer
activate Notif
Notif --> Panel: Confirm notifikasi terkirim
deactivate Notif

Panel --> Admin: Tampilkan konfirmasi kontrak berhasil
Panel --> Admin: Tampilkan preview kontrak PDF

Admin -> Admin: Review kontrak yang dibuat

deactivate Panel

@enduml
