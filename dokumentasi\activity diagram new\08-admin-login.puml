@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho
skinparam nodesep 20
skinparam ranksep 30

title **Activity Diagram - Admin Login**

|Admin|
start
:Aks<PERSON> halaman login admin;
:Input email dan password;
:Klik tombol login;

|Sistem|
:Validasi kredensial admin;
if (Kredensial valid?) then (Ya)
  :Buat session admin;
  :Redirect ke dashboard admin;

  |Admin|
  :Lihat dashboard admin;
  :Aks<PERSON> menu admin;

  |Sistem|
  stop
else (Tidak)
  :Tam<PERSON><PERSON>an pesan error;

  |Admin|
  :<PERSON><PERSON><PERSON> ke halaman login;

  |Sistem|
  stop
endif

@enduml
