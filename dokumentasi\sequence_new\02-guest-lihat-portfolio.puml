@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Guest Lihat Portfolio**

actor Guest
participant "Website ARDFYA" as Website
participant "Database" as DB

Guest -> Website: Klik menu portfolio
activate Website

Website -> DB: Load data portfolio
activate DB
DB --> Website: Return daftar portfolio
deactivate DB

Website --> Guest: Tam<PERSON><PERSON>an halaman portfolio
Website --> Guest: <PERSON><PERSON><PERSON><PERSON> daftar proyek

Guest -> Website: <PERSON>lih portfolio tertentu
Website -> DB: Load detail portfolio
activate DB
DB --> Website: Return detail portfolio dan gambar
deactivate DB

Website --> Guest: <PERSON><PERSON><PERSON><PERSON> detail portfolio
Website --> Guest: <PERSON><PERSON><PERSON><PERSON> gambar proyek
Website --> Guest: Tampilkan deskripsi proyek

Guest -> Guest: Lihat detail portfolio
Guest -> Guest: Evaluasi kualitas pekerjaan

deactivate Website

@enduml
