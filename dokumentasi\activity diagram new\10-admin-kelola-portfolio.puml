@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

' Pengaturan untuk layout yang sangat rapi
skinparam linetype ortho
skinparam nodesep 60
skinparam ranksep 80
skinparam minlen 4
skinparam padding 15

title **Activity Diagram - Admin Kelola Portfolio**

|Admin|
start

:Akses menu kelola portfolio;

|Sistem|
:Load halaman portfolio;

:Tampilkan daftar portfolio;

|Admin|
:Lihat daftar portfolio;

:Pilih tombol aksi;



|Sistem|
:Proses request aksi;

:Tampilkan halaman/form sesuai aksi;

|Admin|
:Lakukan input/konfirmasi;

|Sistem|
:<PERSON><PERSON><PERSON> peruba<PERSON> ke database;

:Update tampilan daftar portfolio;

|Admin|
:<PERSON><PERSON> hasil perubahan;

|Sistem|
stop

@enduml
