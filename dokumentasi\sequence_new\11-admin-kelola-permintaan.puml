@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false
hide footbox

' Styling untuk sequence diagram yang bersih
skinparam participant {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 11
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam sequence {
    ArrowColor black
    ActorBorderColor black
    LifeLineBorderColor black
    ParticipantBorderColor black
    ParticipantBackgroundColor #E1F5FE
    ActorBackgroundColor #FFF3E0
}

title **Sequence Diagram - Admin Kelola Permintaan**

actor Admin
participant "Admin Panel" as Panel
participant "Database" as DB
participant "Notification" as Notif

Admin -> Panel: Akses menu kelola permintaan
activate Panel

Panel -> DB: Load daftar permintaan
activate DB
DB --> Panel: Return daftar permintaan dengan status
deactivate DB

Panel --> Admin: <PERSON><PERSON>lkan halaman permintaan
Panel --> Admin: <PERSON>pi<PERSON>an daftar permintaan

Admin -> Panel: <PERSON><PERSON><PERSON> permintaan yang akan diproses
Panel -> DB: Load detail permintaan
activate DB
DB --> Panel: Return detail permintaan dan file
deactivate DB

Panel --> Admin: Tampilkan detail permintaan

Admin -> Panel: Pilih aksi (terima/tolak/minta info)



Admin -> Panel: Input catatan/alasan
Admin -> Panel: Konfirmasi keputusan

Panel -> DB: Update status permintaan
activate DB
DB --> Panel: Return konfirmasi update
deactivate DB

Panel -> Notif: Kirim notifikasi ke customer
activate Notif
Notif --> Panel: Confirm notifikasi terkirim
deactivate Notif

Panel --> Admin: Tampilkan konfirmasi berhasil
Panel --> Admin: Update tampilan daftar permintaan

Admin -> Admin: Lihat hasil update

deactivate Panel

@enduml
