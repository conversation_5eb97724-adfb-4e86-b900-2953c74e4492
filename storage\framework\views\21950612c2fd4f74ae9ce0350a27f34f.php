<?php $__env->startSection('title', 'Detail Proyek - ' . $project->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-800"><?php echo e($project->name); ?></h1>
                <p class="text-gray-600 mt-2"><?php echo e($project->service->name ?? 'N/A'); ?></p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="px-3 py-1 rounded-full text-sm font-medium
                    <?php if($project->status === 'completed'): ?> bg-green-100 text-green-800
                    <?php elseif($project->status === 'in_progress'): ?> bg-blue-100 text-blue-800
                    <?php elseif($project->status === 'planning'): ?> bg-yellow-100 text-yellow-800
                    <?php elseif($project->status === 'on_hold'): ?> bg-orange-100 text-orange-800
                    <?php elseif($project->status === 'cancelled'): ?> bg-red-100 text-red-800
                    <?php else: ?> bg-gray-100 text-gray-800
                    <?php endif; ?>">
                    <?php switch($project->status):
                        case ('planning'): ?> Perencanaan <?php break; ?>
                        <?php case ('in_progress'): ?> Berlangsung <?php break; ?>
                        <?php case ('on_hold'): ?> Ditunda <?php break; ?>
                        <?php case ('completed'): ?> Selesai <?php break; ?>
                        <?php case ('cancelled'): ?> Dibatalkan <?php break; ?>
                        <?php default: ?> <?php echo e(ucfirst($project->status)); ?>

                    <?php endswitch; ?>
                </span>
                <a href="<?php echo e(route('customer.projects')); ?>" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Kembali
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Project Overview -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Ringkasan Proyek</h2>
                
                <!-- Progress Bar -->
                <?php if($project->progress_percentage !== null): ?>
                    <div class="mb-6">
                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                            <span>Progress Keseluruhan</span>
                            <span class="font-medium"><?php echo e($project->progress_percentage); ?>%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-brand-green h-3 rounded-full transition-all duration-300" 
                                 style="width: <?php echo e($project->progress_percentage); ?>%"></div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Description -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Deskripsi</h3>
                    <p class="text-gray-700 leading-relaxed"><?php echo e($project->description); ?></p>
                </div>

                <!-- Timeline Details -->
                <?php if($project->timeline_details): ?>
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-2">Detail Timeline</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-700"><?php echo e($project->timeline_details); ?></p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Notes -->
                <?php if($project->notes): ?>
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 mb-2">Catatan</h3>
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                            <p class="text-blue-800"><?php echo e($project->notes); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Project Photos -->
            <?php if($project->project_photos && count($project->project_photos) > 0): ?>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Foto Progress</h2>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <?php $__currentLoopData = $project->project_photos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                                <img src="<?php echo e(Storage::url($photo)); ?>" 
                                     alt="Project Photo" 
                                     class="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                                     onclick="openImageModal('<?php echo e(Storage::url($photo)); ?>')">
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Team Assigned -->
            <?php if($project->team_assigned && count($project->team_assigned) > 0): ?>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Tim yang Ditugaskan</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = $project->team_assigned; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="w-10 h-10 bg-brand-green rounded-full flex items-center justify-center text-white font-medium">
                                    <?php echo e(substr($member['name'] ?? 'N', 0, 1)); ?>

                                </div>
                                <div class="ml-3">
                                    <p class="font-medium text-gray-800"><?php echo e($member['name'] ?? 'N/A'); ?></p>
                                    <p class="text-sm text-gray-600"><?php echo e($member['role'] ?? 'Team Member'); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Project Info -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Informasi Proyek</h3>
                
                <div class="space-y-4">
                    <?php if($project->budget): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Budget:</span>
                            <span class="font-medium">Rp <?php echo e(number_format($project->budget, 0, ',', '.')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($project->budget_used): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Budget Terpakai:</span>
                            <span class="font-medium text-orange-600">Rp <?php echo e(number_format($project->budget_used, 0, ',', '.')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($project->start_date): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tanggal Mulai:</span>
                            <span class="font-medium"><?php echo e($project->start_date->format('d M Y')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($project->expected_end_date): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Target Selesai:</span>
                            <span class="font-medium"><?php echo e($project->expected_end_date->format('d M Y')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($project->actual_end_date): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Selesai Aktual:</span>
                            <span class="font-medium text-green-600"><?php echo e($project->actual_end_date->format('d M Y')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($project->address): ?>
                        <div>
                            <span class="text-gray-600">Lokasi:</span>
                            <p class="font-medium mt-1"><?php echo e($project->address); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Related Contract -->
            <?php if($project->contract): ?>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Kontrak Terkait</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Nilai Kontrak:</span>
                            <span class="font-medium text-brand-green">
                                Rp <?php echo e(number_format($project->contract->amount, 0, ',', '.')); ?>

                            </span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span class="px-2 py-1 rounded-full text-xs font-medium
                                <?php switch($project->contract->contract_status):
                                    case ('draft'): ?> bg-gray-100 text-gray-800 <?php break; ?>
                                    <?php case ('active'): ?> bg-green-100 text-green-800 <?php break; ?>
                                    <?php case ('completed'): ?> bg-blue-100 text-blue-800 <?php break; ?>
                                    <?php case ('terminated'): ?> bg-red-100 text-red-800 <?php break; ?>
                                <?php endswitch; ?>">
                                <?php echo e(ucfirst($project->contract->contract_status)); ?>

                            </span>
                        </div>
                        
                        <div class="pt-3 border-t">
                            <a href="<?php echo e(route('customer.contracts')); ?>" 
                               class="w-full bg-brand-green hover:bg-brand-green-dark text-white text-center py-2 rounded transition-colors block">
                                Lihat Detail Kontrak
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Aksi Cepat</h3>
                
                <div class="space-y-3">
                    <a href="<?php echo e(route('messages.customer')); ?>" 
                       class="w-full bg-blue-500 hover:bg-blue-600 text-white text-center py-2 rounded transition-colors block">
                        <i class="fas fa-comments mr-2"></i>Chat dengan Admin
                    </a>
                    
                    <?php if($project->inquiry): ?>
                        <a href="<?php echo e(route('customer.inquiries')); ?>" 
                           class="w-full bg-purple-500 hover:bg-purple-600 text-white text-center py-2 rounded transition-colors block">
                            <i class="fas fa-eye mr-2"></i>Lihat Inquiry Asal
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Last Updated -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="text-sm text-gray-600">
                    <p><strong>Terakhir diperbarui:</strong></p>
                    <p><?php echo e($project->updated_at->format('d M Y, H:i')); ?></p>
                    <p class="text-xs mt-1"><?php echo e($project->updated_at->diffForHumans()); ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <img id="modalImage" src="" alt="Project Photo" class="max-w-full max-h-full object-contain">
        <button onclick="closeImageModal()" 
                class="absolute top-4 right-4 text-white hover:text-gray-300 text-2xl">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<script>
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}

// Close modal when clicking outside the image
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Codingan\CURSOR TA\ardfya_v2\resources\views/customer/project-detail.blade.php ENDPATH**/ ?>