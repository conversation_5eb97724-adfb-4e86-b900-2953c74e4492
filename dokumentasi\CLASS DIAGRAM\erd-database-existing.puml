@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false

' Styling sederhana seperti ERD
skinparam class {
    BackgroundColor white
    BorderColor #495057
    BorderThickness 2
    FontSize 12
    AttributeFontSize 11
    AttributeFontColor #666666
}

skinparam arrow {
    Color #495057
    Thickness 2
}

title **ERD - Database ARDFYA Existing**

' Entitas berdasarkan database yang ada
class users {
    - id: bigint(20) PK
    - name: varchar(255)
    - email: varchar(255) UNIQUE
    - password: varchar(255)
    - phone: varchar(255)
    - address: text
    - role: enum('customer','admin')
    - email_verified_at: timestamp
    - notification_settings: json
    - remember_token: varchar(100)
    - created_at: timestamp
    - updated_at: timestamp
}

class services {
    - id: bigint(20) PK
    - name: varchar(255)
    - description: text
    - icon: varchar(255)
    - is_active: tinyint(1)
    - created_at: timestamp
    - updated_at: timestamp
}

class inquiries {
    - id: bigint(20) PK
    - name: varchar(255)
    - email: varchar(255)
    - phone: varchar(255)
    - service_id: bigint(20) FK
    - address: text
    - property_type: varchar(255)
    - area_size: int(11)
    - current_condition: varchar(255)
    - description: text
    - budget: decimal(12,2)
    - start_date: date
    - schedule_flexibility: varchar(255)
    - status: enum('new','contacted','in_progress','completed','cancelled')
    - admin_notes: text
    - user_id: bigint(20) FK
    - created_at: timestamp
    - updated_at: timestamp
}

class projects {
    - id: bigint(20) PK
    - name: varchar(255)
    - user_id: bigint(20) FK
    - service_id: bigint(20) FK
    - inquiry_id: bigint(20) FK
    - description: text
    - start_date: date
    - end_date: date
    - expected_end_date: date
    - actual_end_date: date
    - status: enum('planning','in_progress','on_hold','completed','cancelled')
    - address: text
    - total_cost: decimal(12,2)
    - category: varchar(255)
    - is_featured: tinyint(1)
    - budget: decimal(12,2)
    - budget_used: decimal(15,2)
    - notes: text
    - timeline_details: text
    - team_assigned: json
    - project_photos: json
    - progress_percentage: int(11)
    - customer_last_viewed: timestamp
    - created_at: timestamp
    - updated_at: timestamp
}

class contracts {
    - id: bigint(20) PK
    - contract_number: varchar(255)
    - project_id: bigint(20) FK
    - user_id: bigint(20) FK
    - start_date: date
    - end_date: date
    - amount: decimal(12,2)
    - contract_status: enum('draft','active','completed','terminated')
    - installments: int(11)
    - notes: text
    - created_at: timestamp
    - updated_at: timestamp
}

class chats {
    - id: bigint(20) PK
    - customer_id: bigint(20) FK
    - admin_id: bigint(20) FK
    - message: text
    - is_from_admin: tinyint(1)
    - is_read: tinyint(1)
    - file_url: varchar(255)
    - file_name: varchar(255)
    - file_type: varchar(255)
    - file_size: int(11)
    - created_at: timestamp
    - updated_at: timestamp
    - deleted_at: timestamp
}

class portfolios {
    - id: bigint(20) PK
    - title: varchar(255)
    - description: text
    - category: varchar(255)
    - image_path: varchar(255)
    - client_name: varchar(255)
    - location: varchar(255)
    - completion_date: date
    - project_value: decimal(15,2)
    - is_featured: tinyint(1)
    - is_active: tinyint(1)
    - ordering: int(11)
    - created_at: timestamp
    - updated_at: timestamp
}

class messages {
    - id: bigint(20) PK
    - user_id: bigint(20) FK
    - project_id: bigint(20) FK
    - inquiry_id: bigint(20) FK
    - message: text
    - is_from_admin: tinyint(1)
    - is_read: tinyint(1)
    - created_at: timestamp
    - updated_at: timestamp
}

' Hubungan Foreign Key
users ||--o{ inquiries : "user_id"
users ||--o{ projects : "user_id"
users ||--o{ contracts : "user_id"
users ||--o{ chats : "customer_id"
users ||--o{ chats : "admin_id"
users ||--o{ messages : "user_id"

services ||--o{ inquiries : "service_id"
services ||--o{ projects : "service_id"

inquiries ||--o{ projects : "inquiry_id"
inquiries ||--o{ messages : "inquiry_id"

projects ||--o{ contracts : "project_id"
projects ||--o{ messages : "project_id"

' Catatan untuk implementasi
note top of users
    **Role**: 'customer' atau 'admin'
    Tabel utama untuk semua pengguna
end note

note right of chats
    **File support**: URL, name, type, size
    **Soft delete**: deleted_at
end note

note bottom of portfolios
    **Featured**: is_featured untuk highlight
    **Ordering**: untuk urutan tampil
end note

@enduml
