@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho

title **Activity Diagram - Customer Chat**

|Customer|
start
:Akses menu chat;

|Sistem|
:Load halaman chat;
:Tampilkan riwayat chat;
:Cek status admin online;

|Customer|
:Lihat riwayat chat;
:Ketik pesan baru;
:<PERSON><PERSON> pesan;

|Sistem|
:Simpan pesan ke database;
:<PERSON><PERSON> notifikasi ke admin;
:Update tampilan chat;

|Customer|
:<PERSON><PERSON> pesan terkirim;
if (<PERSON> balasan admin?) then (Ya)
  |Sistem|
  :<PERSON><PERSON><PERSON><PERSON> pesan dari admin;

  |Customer|
  :<PERSON>ca balasan admin;
  :<PERSON><PERSON> pesan (opsional);
  :Selesai chat;

  |Sistem|
  stop
else (Tidak)
  |Customer|
  :Menunggu balasan;
  :Selesai chat;

  |Sistem|
  stop
endif

@enduml
