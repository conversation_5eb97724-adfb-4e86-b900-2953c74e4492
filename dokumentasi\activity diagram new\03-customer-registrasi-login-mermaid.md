# Activity Diagram - Customer Login

```mermaid
flowchart TD
    Start([Start]) --> A1[Aks<PERSON> halaman login]
    
    subgraph Customer ["👤 Customer"]
        A1[Aks<PERSON> halaman login]
        A2[Input email dan password]
        A3[Klik tombol login]
        A4[Akses dashboard customer]
        A5[Kembali ke halaman login]
    end
    
    subgraph Sistem ["🖥️ Sistem"]
        S1[Validasi kredensial]
        S2{Kredensial valid?}
        S3[Buat session customer]
        S4[Redirect ke dashboard]
        S5[Tampilkan pesan error]
    end
    
    %% Flow connections
    A1 --> A2
    A2 --> A3
    A3 --> S1
    S1 --> S2
    
    %% Decision branches
    S2 -->|Ya| S3
    S3 --> S4
    S4 --> A4
    A4 --> End1([Stop])
    
    S2 -->|Tidak| S5
    S5 --> A5
    A5 --> End2([Stop])
    
    %% Styling
    classDef customerClass fill:#E1F5FE,stroke:#0277BD,stroke-width:2px,color:#000
    classDef sistemClass fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef decisionClass fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef startEndClass fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    
    class A1,A2,A3,A4,A5 customerClass
    class S1,S3,S4,S5 sistemClass
    class S2 decisionClass
    class Start,End1,End2 startEndClass
```

## Deskripsi Diagram

Diagram aktivitas ini menggambarkan alur kerja customer saat melakukan proses login ke sistem ARDFYA:

### Swimlane Customer:
- **Akses halaman login**: Customer mengunjungi halaman login
- **Input email dan password**: Customer memasukkan kredensial
- **Klik tombol login**: Customer mengirim form login
- **Akses dashboard customer**: Customer berhasil masuk ke dashboard (jika valid)
- **Kembali ke halaman login**: Customer kembali ke login (jika gagal)

### Swimlane Sistem:
- **Validasi kredensial**: Sistem memverifikasi email dan password
- **Kredensial valid?**: Decision point untuk validasi
- **Buat session customer**: Sistem membuat session untuk customer
- **Redirect ke dashboard**: Sistem mengarahkan ke dashboard customer
- **Tampilkan pesan error**: Sistem menampilkan pesan kesalahan

### Decision Points:
- **Kredensial valid?**
  - **Ya**: Buat session → Redirect ke dashboard → Customer akses dashboard
  - **Tidak**: Tampilkan error → Customer kembali ke login

### Karakteristik:
- **Actor**: Customer
- **Trigger**: Customer ingin mengakses dashboard
- **Precondition**: Customer memiliki akun yang terdaftar
- **Postcondition**: Customer berhasil login atau mendapat pesan error

### Alur Proses:
1. Customer mengakses halaman login
2. Customer memasukkan email dan password
3. Sistem memvalidasi kredensial
4. Jika valid: buat session dan redirect ke dashboard
5. Jika tidak valid: tampilkan error dan kembali ke login
