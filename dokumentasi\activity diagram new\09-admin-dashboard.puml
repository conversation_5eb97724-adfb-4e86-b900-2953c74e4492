@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho

title **Activity Diagram - Admin Dashboard**

|Admin|
start
:Login ke panel admin;

|Sistem|
:Validasi session admin;
:Load dashboard admin;
:Tampilkan statistik sistem;
:Tampilkan menu navigasi;
:Tampilkan notifikasi;

|Admin|
:Lihat dashboard;
:Pilih menu yang diinginkan;

|Sistem|
if (<PERSON>u yang dipilih?) then (<PERSON><PERSON><PERSON> Portfolio)
  :Redirect ke halaman portfolio;
  stop
else if (<PERSON><PERSON><PERSON>)
  :Redirect ke halaman inquiry;
  stop
else if (<PERSON><PERSON>la Proyek)
  :Redirect ke halaman proyek;
  stop
else if (Buat Kontrak)
  :Redirect ke halaman kontrak;
  stop
else (Logout)
  :Hapus session admin;
  :Redirect ke halaman login;
  stop
endif

@enduml
