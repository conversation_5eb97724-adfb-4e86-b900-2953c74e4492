@startuml

!theme plain
skinparam backgroundColor white
skinparam shadowing false
skinparam handwritten false

' Styling untuk swimlane yang rapi
skinparam activity {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    BorderThickness 2
    FontSize 11
}

skinparam activityDiamond {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    BorderThickness 2
    FontSize 10
}

skinparam activityStart {
    Color black
}

skinparam activityEnd {
    Color black
}

skinparam arrow {
    Color black
    Thickness 2
}

skinparam swimlane {
    BorderColor black
    BorderThickness 2
    TitleBackgroundColor #F5F5F5
}

skinparam linetype ortho

title **Activity Diagram - Admin Buat Kontrak**

|Admin|
start
:Akses menu buat kontrak;

|Sistem|
:<PERSON>ad halaman kontrak;
:Tampilkan daftar proyek;

|Admin|
:Pilih proyek untuk kontrak;

|Sistem|
:Load data proyek;
:Tampilkan form kontrak;

|Admin|
:Isi detail kontrak;
:Input nilai kontrak;
:Input syarat dan ketentuan;
:Input jadwal pembayaran;
:Preview kontrak;

|Sistem|
:Generate preview kontrak PDF;

|Admin|
:Review kontrak;
if (Kontrak sudah benar?) then (Ya)
  :Submit kontrak;

  |Sistem|
  :Simpan kontrak ke database;
  :Generate kontrak PDF final;
  :Kirim notifikasi ke customer;
  :Update status proyek;

  |Admin|
  :Kontrak berhasil dibuat;

  |Sistem|
  stop
else (Tidak)
  |Admin|
  :Edit kontrak;

  |Sistem|
  :Kembali ke form edit;
  stop
endif

@enduml
