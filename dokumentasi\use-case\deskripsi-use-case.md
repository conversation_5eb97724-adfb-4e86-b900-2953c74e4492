# Deskripsi Use Case - Sistem Manajemen Konstruksi ARDFYA v2.1

## 1. G<PERSON>baran <PERSON>um

Dokumen ini menjelaskan secara detail setiap use case dalam sistem manajemen konstruksi ARDFYA v2.1. Sistem ini melayani tiga jenis aktor utama:

- **<PERSON><PERSON><PERSON><PERSON> (Guest)**: Pengguna yang belum terdaftar atau belum login
- **<PERSON><PERSON><PERSON><PERSON> (Customer)**: Pengguna terdaftar yang dapat mengajukan proyek
- **Administrator (Admin)**: Pengelola sistem dengan akses penuh

## 2. Use Case untuk Pengunjung (Guest)

### UC01 - Melihat Beranda
- **Deskripsi**: Pengunjung dapat melihat halaman utama website
- **Aktor**: Pengunjung
- **Prasyarat**: Tidak ada
- **Alur Utama**: 
  1. Pengunjung mengakses website
  2. Sistem menampilkan beranda dengan portfolio unggulan
  3. Pengunjung dapat melihat informasi perusahaan dan layanan

### UC02 - Melihat Galeri Portfolio
- **Deskripsi**: Pengunjung dapat melihat galeri portfolio proyek
- **Aktor**: Pengunjung
- **Prasyarat**: Tidak ada
- **Alur Utama**:
  1. Pengunjung mengklik menu Portfolio
  2. Sistem menampilkan galeri portfolio dengan filter kategori
  3. Pengunjung dapat memfilter berdasarkan kategori

### UC03 - Melihat Detail Portfolio
- **Deskripsi**: Pengunjung dapat melihat detail proyek portfolio
- **Aktor**: Pengunjung
- **Prasyarat**: Portfolio tersedia
- **Alur Utama**:
  1. Pengunjung mengklik portfolio tertentu
  2. Sistem menampilkan detail portfolio (gambar, deskripsi, spesifikasi)

### UC04 - Mengisi Form Kontak
- **Deskripsi**: Pengunjung dapat mengirim pesan melalui form kontak
- **Aktor**: Pengunjung
- **Prasyarat**: Tidak ada
- **Alur Utama**:
  1. Pengunjung mengakses halaman kontak
  2. Pengunjung mengisi form (nama, email, pesan)
  3. Sistem mengirim email ke admin

### UC05 - Mengajukan Pertanyaan
- **Deskripsi**: Pengunjung dapat mengajukan inquiry untuk proyek
- **Aktor**: Pengunjung
- **Prasyarat**: Tidak ada
- **Alur Utama**:
  1. Pengunjung mengisi form inquiry
  2. Sistem menyimpan inquiry
  3. Admin menerima notifikasi inquiry baru

### UC06 - Mendaftar Akun
- **Deskripsi**: Pengunjung dapat mendaftar sebagai pelanggan
- **Aktor**: Pengunjung
- **Prasyarat**: Email belum terdaftar
- **Alur Utama**:
  1. Pengunjung mengisi form registrasi
  2. Sistem memvalidasi data
  3. Akun pelanggan dibuat

### UC07 - Masuk ke Sistem
- **Deskripsi**: Pengguna dapat login ke sistem
- **Aktor**: Pengunjung
- **Prasyarat**: Memiliki akun terdaftar
- **Alur Utama**:
  1. Pengguna memasukkan email dan password
  2. Sistem memvalidasi kredensial
  3. Pengguna diarahkan ke dashboard sesuai role

### UC08 - Melihat Halaman Tentang
- **Deskripsi**: Pengunjung dapat melihat informasi perusahaan
- **Aktor**: Pengunjung
- **Prasyarat**: Tidak ada

### UC09 - Melihat Layanan
- **Deskripsi**: Pengunjung dapat melihat daftar layanan yang tersedia
- **Aktor**: Pengunjung
- **Prasyarat**: Tidak ada

## 3. Use Case untuk Pelanggan (Customer)

### UC10 - Mengakses Dashboard Pelanggan
- **Deskripsi**: Pelanggan dapat mengakses dashboard pribadi
- **Aktor**: Pelanggan
- **Prasyarat**: Sudah login sebagai pelanggan
- **Alur Utama**:
  1. Pelanggan login ke sistem
  2. Sistem menampilkan dashboard dengan ringkasan proyek
  3. Pelanggan dapat melihat status proyek aktif

### UC11 - Mengelola Profil
- **Deskripsi**: Pelanggan dapat mengedit informasi profil
- **Aktor**: Pelanggan
- **Prasyarat**: Sudah login
- **Alur Utama**:
  1. Pelanggan mengakses halaman profil
  2. Pelanggan mengedit informasi (nama, telepon, alamat)
  3. Sistem menyimpan perubahan

### UC12 - Mengajukan Inquiry
- **Deskripsi**: Pelanggan dapat mengajukan inquiry proyek baru
- **Aktor**: Pelanggan
- **Prasyarat**: Sudah login
- **Alur Utama**:
  1. Pelanggan mengisi form inquiry
  2. Sistem menyimpan inquiry dengan status "Pending"
  3. Admin menerima notifikasi

### UC13 - Melacak Status Proyek
- **Deskripsi**: Pelanggan dapat melihat status proyek mereka
- **Aktor**: Pelanggan
- **Prasyarat**: Memiliki proyek aktif
- **Alur Utama**:
  1. Pelanggan mengakses daftar proyek
  2. Sistem menampilkan status terkini setiap proyek

### UC14 - Melihat Detail Proyek
- **Deskripsi**: Pelanggan dapat melihat detail proyek mereka
- **Aktor**: Pelanggan
- **Prasyarat**: Memiliki proyek
- **Alur Utama**:
  1. Pelanggan mengklik proyek tertentu
  2. Sistem menampilkan detail proyek (timeline, dokumen, progress)

### UC15 - Mengunduh Kontrak
- **Deskripsi**: Pelanggan dapat mengunduh kontrak proyek
- **Aktor**: Pelanggan
- **Prasyarat**: Kontrak sudah dibuat admin
- **Alur Utama**:
  1. Pelanggan mengakses halaman kontrak
  2. Pelanggan mengklik tombol unduh
  3. Sistem menghasilkan file PDF kontrak

### UC16 - Melihat Pratinjau Kontrak
- **Deskripsi**: Pelanggan dapat melihat pratinjau kontrak di browser
- **Aktor**: Pelanggan
- **Prasyarat**: Kontrak tersedia
- **Alur Utama**:
  1. Pelanggan mengklik pratinjau kontrak
  2. Sistem menampilkan kontrak dalam format PDF di browser

### UC17 - Chat dengan Admin
- **Deskripsi**: Pelanggan dapat berkomunikasi dengan admin via chat
- **Aktor**: Pelanggan
- **Prasyarat**: Sudah login
- **Alur Utama**:
  1. Pelanggan mengakses fitur chat
  2. Pelanggan mengirim pesan
  3. Admin menerima notifikasi real-time

### UC18 - Mengirim Pesan
- **Deskripsi**: Pelanggan dapat mengirim pesan dalam chat
- **Aktor**: Pelanggan
- **Prasyarat**: Chat session aktif

### UC19 - Melihat Riwayat Chat
- **Deskripsi**: Pelanggan dapat melihat riwayat percakapan
- **Aktor**: Pelanggan
- **Prasyarat**: Pernah melakukan chat

### UC20 - Keluar dari Sistem
- **Deskripsi**: Pelanggan dapat logout dari sistem
- **Aktor**: Pelanggan
- **Prasyarat**: Sudah login

## 4. Use Case untuk Administrator (Admin)

### UC21 - Mengakses Dashboard Admin
- **Deskripsi**: Admin dapat mengakses dashboard administrasi
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin login dengan kredensial admin
  2. Sistem menampilkan dashboard dengan statistik
  3. Admin dapat melihat ringkasan inquiry, proyek, dan pelanggan

### UC22 - Mengelola Data Pelanggan
- **Deskripsi**: Admin dapat mengelola informasi pelanggan
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses halaman pelanggan
  2. Admin dapat melihat, mengedit, atau menghapus data pelanggan
  3. Sistem menyimpan perubahan

### UC23 - Mengelola Inquiry
- **Deskripsi**: Admin dapat mengelola inquiry dari pelanggan
- **Aktor**: Administrator
- **Prasyarat**: Ada inquiry yang masuk
- **Alur Utama**:
  1. Admin melihat daftar inquiry
  2. Admin dapat mengubah status inquiry
  3. Admin dapat mengkonversi inquiry menjadi proyek

### UC24 - Mengelola Proyek
- **Deskripsi**: Admin dapat mengelola proyek pelanggan
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses halaman proyek
  2. Admin dapat membuat, mengedit, atau menghapus proyek
  3. Admin dapat memperbarui status dan progress proyek

### UC25 - Mengelola Kontrak
- **Deskripsi**: Admin dapat mengelola kontrak proyek
- **Aktor**: Administrator
- **Prasyarat**: Proyek tersedia
- **Alur Utama**:
  1. Admin membuat kontrak untuk proyek
  2. Admin mengisi detail kontrak
  3. Sistem menghasilkan dokumen kontrak PDF

### UC26 - Mengelola Layanan
- **Deskripsi**: Admin dapat mengelola layanan yang ditawarkan
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses halaman layanan
  2. Admin dapat menambah, mengedit, atau menghapus layanan
  3. Admin dapat mengatur layanan unggulan

### UC27 - Melihat Analitik
- **Deskripsi**: Admin dapat melihat analitik dan statistik sistem
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses halaman analitik
  2. Sistem menampilkan grafik dan statistik
  3. Admin dapat melihat tren inquiry, proyek, dan pelanggan

### UC28 - Membuat Laporan
- **Deskripsi**: Admin dapat membuat laporan sistem
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin memilih jenis laporan
  2. Admin menentukan periode laporan
  3. Sistem menghasilkan laporan dalam format PDF/Excel

### UC29 - Chat dengan Pelanggan
- **Deskripsi**: Admin dapat berkomunikasi dengan pelanggan via chat
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin melihat daftar chat aktif
  2. Admin memilih pelanggan untuk chat
  3. Admin mengirim dan menerima pesan real-time

### UC30 - Konfigurasi Sistem
- **Deskripsi**: Admin dapat mengkonfigurasi pengaturan sistem
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin dengan hak akses penuh
- **Alur Utama**:
  1. Admin mengakses halaman konfigurasi
  2. Admin mengubah pengaturan sistem
  3. Sistem menyimpan konfigurasi baru

## 5. Use Case untuk Manajemen Portfolio (Fitur Baru v2.1)

### UC31 - Membuat Portfolio Baru
- **Deskripsi**: Admin dapat membuat portfolio proyek baru
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses halaman portfolio
  2. Admin mengklik tombol "Tambah Portfolio"
  3. Admin mengisi form portfolio (judul, deskripsi, kategori, gambar)
  4. Sistem menyimpan portfolio baru

### UC32 - Mengedit Portfolio
- **Deskripsi**: Admin dapat mengedit portfolio yang sudah ada
- **Aktor**: Administrator
- **Prasyarat**: Portfolio tersedia
- **Alur Utama**:
  1. Admin memilih portfolio yang akan diedit
  2. Admin mengubah informasi portfolio
  3. Sistem menyimpan perubahan

### UC33 - Menghapus Portfolio
- **Deskripsi**: Admin dapat menghapus portfolio
- **Aktor**: Administrator
- **Prasyarat**: Portfolio tersedia
- **Alur Utama**:
  1. Admin memilih portfolio yang akan dihapus
  2. Sistem meminta konfirmasi penghapusan
  3. Admin mengkonfirmasi penghapusan
  4. Sistem menghapus portfolio dan file terkait

### UC34 - Mengunggah Gambar Portfolio
- **Deskripsi**: Admin dapat mengunggah gambar untuk portfolio
- **Aktor**: Administrator
- **Prasyarat**: Sedang membuat/mengedit portfolio
- **Alur Utama**:
  1. Admin memilih file gambar
  2. Sistem memvalidasi format dan ukuran gambar
  3. Sistem mengunggah dan menyimpan gambar
  4. Gambar ditampilkan dalam portfolio

### UC35 - Mengatur Portfolio Unggulan
- **Deskripsi**: Admin dapat menentukan portfolio unggulan
- **Aktor**: Administrator
- **Prasyarat**: Portfolio tersedia
- **Alur Utama**:
  1. Admin memilih portfolio
  2. Admin mengaktifkan status "unggulan"
  3. Portfolio ditampilkan di beranda

### UC36 - Mengelola Kategori Portfolio
- **Deskripsi**: Admin dapat mengelola kategori portfolio
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses pengaturan kategori
  2. Admin dapat menambah, mengedit, atau menghapus kategori
  3. Sistem memperbarui filter kategori

### UC37 - Melihat Analitik Portfolio
- **Deskripsi**: Admin dapat melihat statistik portfolio
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses analitik portfolio
  2. Sistem menampilkan statistik views, kategori populer
  3. Admin dapat melihat performa setiap portfolio

## 6. Use Case untuk Manajemen Inquiry

### UC38 - Melihat Daftar Inquiry
- **Deskripsi**: Admin dapat melihat semua inquiry yang masuk
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses halaman inquiry
  2. Sistem menampilkan daftar inquiry dengan status
  3. Admin dapat memfilter berdasarkan status atau tanggal

### UC39 - Memperbarui Status Inquiry
- **Deskripsi**: Admin dapat mengubah status inquiry
- **Aktor**: Administrator
- **Prasyarat**: Inquiry tersedia
- **Alur Utama**:
  1. Admin memilih inquiry
  2. Admin mengubah status (Pending, In Review, Approved, Rejected)
  3. Sistem mengirim notifikasi ke pelanggan

### UC40 - Mengkonversi Inquiry ke Proyek
- **Deskripsi**: Admin dapat mengkonversi inquiry menjadi proyek
- **Aktor**: Administrator
- **Prasyarat**: Inquiry dengan status approved
- **Alur Utama**:
  1. Admin memilih inquiry yang akan dikonversi
  2. Admin mengisi detail proyek tambahan
  3. Sistem membuat proyek baru dari inquiry
  4. Status inquiry berubah menjadi "Converted"

### UC41 - Menghapus Inquiry
- **Deskripsi**: Admin dapat menghapus inquiry
- **Aktor**: Administrator
- **Prasyarat**: Inquiry tersedia
- **Alur Utama**:
  1. Admin memilih inquiry yang akan dihapus
  2. Sistem meminta konfirmasi
  3. Admin mengkonfirmasi penghapusan
  4. Inquiry dihapus dari sistem

### UC42 - Mencari Inquiry
- **Deskripsi**: Admin dapat mencari inquiry berdasarkan kriteria
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin memasukkan kata kunci pencarian
  2. Sistem mencari berdasarkan nama pelanggan, layanan, atau deskripsi
  3. Sistem menampilkan hasil pencarian

## 7. Use Case untuk Manajemen Proyek

### UC43 - Membuat Proyek Baru
- **Deskripsi**: Admin dapat membuat proyek baru
- **Aktor**: Administrator
- **Prasyarat**: Login sebagai admin
- **Alur Utama**:
  1. Admin mengakses halaman proyek
  2. Admin mengklik "Tambah Proyek"
  3. Admin mengisi detail proyek (nama, pelanggan, layanan, timeline)
  4. Sistem menyimpan proyek baru

### UC44 - Memperbarui Status Proyek
- **Deskripsi**: Admin dapat mengubah status proyek
- **Aktor**: Administrator
- **Prasyarat**: Proyek tersedia
- **Alur Utama**:
  1. Admin memilih proyek
  2. Admin mengubah status (Planning, In Progress, Completed, On Hold)
  3. Sistem memperbarui timeline dan mengirim notifikasi

### UC45 - Mengelola Timeline Proyek
- **Deskripsi**: Admin dapat mengelola jadwal proyek
- **Aktor**: Administrator
- **Prasyarat**: Proyek tersedia
- **Alur Utama**:
  1. Admin mengakses timeline proyek
  2. Admin menambah/mengedit milestone
  3. Admin mengatur tanggal mulai dan selesai
  4. Sistem memperbarui timeline

### UC46 - Mengunggah Dokumen Proyek
- **Deskripsi**: Admin dapat mengunggah dokumen terkait proyek
- **Aktor**: Administrator
- **Prasyarat**: Proyek tersedia
- **Alur Utama**:
  1. Admin memilih file dokumen
  2. Sistem memvalidasi format file
  3. Dokumen diunggah dan dikaitkan dengan proyek
  4. Pelanggan dapat mengakses dokumen

### UC47 - Membuat Kontrak dari Proyek
- **Deskripsi**: Admin dapat membuat kontrak berdasarkan proyek
- **Aktor**: Administrator
- **Prasyarat**: Proyek dengan status approved
- **Alur Utama**:
  1. Admin memilih proyek untuk dibuatkan kontrak
  2. Admin mengisi template kontrak
  3. Sistem menghasilkan dokumen kontrak PDF
  4. Kontrak tersedia untuk diunduh pelanggan

## 8. Use Case untuk Sistem Komunikasi

### UC48 - Mengirim Pesan Real-time
- **Deskripsi**: Pengguna dapat mengirim pesan secara real-time
- **Aktor**: Pelanggan, Administrator
- **Prasyarat**: Login dan chat session aktif
- **Alur Utama**:
  1. Pengguna mengetik pesan
  2. Pengguna menekan tombol kirim
  3. Sistem mengirim pesan via WebSocket
  4. Penerima menerima pesan secara real-time

### UC49 - Menerima Notifikasi Pesan
- **Deskripsi**: Pengguna menerima notifikasi pesan baru
- **Aktor**: Pelanggan, Administrator
- **Prasyarat**: Chat session aktif
- **Alur Utama**:
  1. Sistem menerima pesan baru
  2. Sistem mengirim notifikasi push
  3. Pengguna melihat indikator pesan baru
  4. Counter pesan belum dibaca diperbarui

### UC50 - Menandai Pesan Dibaca
- **Deskripsi**: Pengguna dapat menandai pesan sebagai sudah dibaca
- **Aktor**: Pelanggan, Administrator
- **Prasyarat**: Ada pesan yang belum dibaca
- **Alur Utama**:
  1. Pengguna membuka chat
  2. Sistem otomatis menandai pesan sebagai dibaca
  3. Status "read" dikirim ke pengirim
  4. Counter pesan belum dibaca direset

### UC51 - Melihat Status Online
- **Deskripsi**: Pengguna dapat melihat status online lawan chat
- **Aktor**: Pelanggan, Administrator
- **Prasyarat**: Chat session aktif
- **Alur Utama**:
  1. Sistem melacak status online pengguna
  2. Status ditampilkan dalam interface chat
  3. Indikator "online/offline" diperbarui real-time

### UC52 - Mencari Riwayat Chat
- **Deskripsi**: Pengguna dapat mencari pesan dalam riwayat chat
- **Aktor**: Pelanggan, Administrator
- **Prasyarat**: Ada riwayat chat
- **Alur Utama**:
  1. Pengguna memasukkan kata kunci pencarian
  2. Sistem mencari dalam riwayat pesan
  3. Hasil pencarian ditampilkan dengan highlight
  4. Pengguna dapat melompat ke pesan yang dicari

## 9. Relasi Antar Use Case

### Include Relationships
- **UC06 (Mendaftar Akun) include UC07 (Masuk ke Sistem)**: Setelah registrasi berhasil, pengguna otomatis login
- **UC15 (Mengunduh Kontrak) include UC16 (Melihat Pratinjau Kontrak)**: Sebelum mengunduh, pengguna dapat melihat pratinjau

### Extend Relationships
- **UC12 (Mengajukan Inquiry) extend UC40 (Mengkonversi Inquiry ke Proyek)**: Inquiry yang disetujui dapat dikonversi menjadi proyek
- **UC24 (Mengelola Proyek) extend UC47 (Membuat Kontrak dari Proyek)**: Proyek yang approved dapat dibuatkan kontrak

## 10. Catatan Implementasi

### Teknologi Pendukung
- **Real-time Communication**: Laravel Echo + Pusher untuk chat real-time
- **File Management**: Laravel Storage untuk upload gambar dan dokumen
- **PDF Generation**: DomPDF untuk membuat kontrak dan laporan
- **Authentication**: Laravel Sanctum untuk autentikasi API
- **Notification**: Laravel Notification untuk email dan push notification

### Keamanan
- Semua use case yang memerlukan autentikasi dilindungi middleware
- Admin use case dilindungi dengan AdminMiddleware
- File upload divalidasi untuk mencegah security risk
- CSRF protection untuk semua form submission

### Performance
- Lazy loading untuk daftar yang panjang
- Caching untuk data yang sering diakses
- Image optimization untuk portfolio
- Database indexing untuk pencarian yang cepat

