@startuml Use Case Diagram - ARDFYA

!theme plain
skinparam handwritten false
skinparam shadowing false

' Styling sederhana
skinparam usecase {
    BackgroundColor #E1F5FE
    BorderColor #0288D1
    FontSize 11
    BorderThickness 2
}

skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #01579B
    FontSize 12
    BorderThickness 2
}

skinparam rectangle {
    BackgroundColor #FAFAFA
    BorderColor #424242
    FontSize 14
    BorderThickness 3
}

title **Use Case Diagram - ARDFYA Construction Management System**

' Actors
actor "Guest" as G
actor "Customer" as C
actor "Admin" as A

' System boundary dengan semua use case dalam satu area
rectangle "**ARDFYA System**" {
  
  ' Use cases dengan kata kerja - dijajarkan dalam grid
  
  ' Row 1 - Public Actions
  usecase "Browse Website" as UC1
  usecase "View Portfolio" as UC2
  usecase "Submit Inquiry" as UC3
  usecase "Register" as UC4
  
  ' Row 2 - Authentication & Customer Actions
  usecase "Login" as UC5
  usecase "Access Dashboard" as UC6
  usecase "Track Project" as UC7
  usecase "Chat" as UC8
  
  ' Row 3 - Customer Management Actions
  usecase "View Contract" as UC9
  usecase "Download Document" as UC10
  usecase "Update Profile" as UC11
  usecase "Manage Customer" as UC12
  
  ' Row 4 - Admin Management Actions
  usecase "Process Inquiry" as UC13
  usecase "Manage Project" as UC14
  usecase "Create Portfolio" as UC15
  usecase "Generate Contract" as UC16
  
  ' Layout positioning untuk grid yang rapi
  UC1 -[hidden]- UC2
  UC2 -[hidden]- UC3
  UC3 -[hidden]- UC4
  
  UC5 -[hidden]- UC6
  UC6 -[hidden]- UC7
  UC7 -[hidden]- UC8
  
  UC9 -[hidden]- UC10
  UC10 -[hidden]- UC11
  UC11 -[hidden]- UC12
  
  UC13 -[hidden]- UC14
  UC14 -[hidden]- UC15
  UC15 -[hidden]- UC16
  
  UC1 -[hidden]d- UC5
  UC2 -[hidden]d- UC6
  UC3 -[hidden]d- UC7
  UC4 -[hidden]d- UC8
  
  UC5 -[hidden]d- UC9
  UC6 -[hidden]d- UC10
  UC7 -[hidden]d- UC11
  UC8 -[hidden]d- UC12
  
  UC9 -[hidden]d- UC13
  UC10 -[hidden]d- UC14
  UC11 -[hidden]d- UC15
  UC12 -[hidden]d- UC16
}

' Guest relationships
G --> UC1 : browses
G --> UC2 : views
G --> UC3 : submits
G --> UC4 : registers
G --> UC5 : logs in

' Customer relationships
C --> UC1 : browses
C --> UC2 : views
C --> UC3 : submits
C --> UC5 : logs in
C --> UC6 : accesses
C --> UC7 : tracks
C --> UC8 : chats
C --> UC9 : views
C --> UC11 : updates

' Admin relationships
A --> UC1 : browses
A --> UC2 : views
A --> UC5 : logs in
A --> UC8 : chats
A --> UC12 : manages
A --> UC13 : processes
A --> UC14 : manages
A --> UC15 : creates
A --> UC16 : generates

' Include relationships (selalu dilakukan)
UC4 ..> UC5 : <<include>>\n//register includes login//
UC9 ..> UC10 : <<include>>\n//view contract includes download//
UC8 ..> UC8 : <<include>>\n//chat is bidirectional//

' Extend relationships (opsional/kondisional)
UC13 ..> UC14 : <<extend>>\n//approved inquiry becomes project//
UC14 ..> UC16 : <<extend>>\n//completed project gets contract//
UC6 ..> UC7 : <<extend>>\n//dashboard may show project tracking//

@enduml
